import axios, { AxiosError } from 'axios'
import { Request, Response } from 'express'
import { StatusCodes } from 'http-status-codes'
import { oauthClientService } from '../services/oauthClientService'

export const getAllClients = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const clients = await oauthClientService.getAllClients()
    res.json(clients)
  } catch (error) {
    console.error('Error getting clients:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      error: 'server_error',
      message: 'Failed to retrieve clients',
    })
  }
}

export const getClientById = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const id = parseInt(req.params.id)
    if (isNaN(id)) {
      res.status(StatusCodes.BAD_REQUEST).json({
        error: 'invalid_request',
        message: 'Invalid client ID',
      })
      return
    }

    const client = await oauthClientService.getClientById(id)
    if (!client) {
      res.status(StatusCodes.NOT_FOUND).json({
        error: 'not_found',
        message: 'Client not found',
      })
      return
    }

    res.json(client)
  } catch (error) {
    console.error('Error getting client:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      error: 'server_error',
      message: 'Failed to retrieve client',
    })
  }
}

export const createClient = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const {
      client_name,
      redirect_uris,
      grant_types,
      scope,
      is_active,
      webhook_url,
      webhook_secret,
      webhook_events,
    } = req.body

    if (!client_name || !redirect_uris || !grant_types || !scope) {
      res.status(StatusCodes.BAD_REQUEST).json({
        error: 'invalid_request',
        message:
          'Client name, redirect URIs, grant types, and scope are required',
      })
      return
    }

    // Validate grant types
    const validGrantTypes = [
      'authorization_code',
      'password',
      'refresh_token',
      'client_credentials',
    ]
    const invalidGrantTypes = grant_types.filter(
      (type: string) => !validGrantTypes.includes(type)
    )
    if (invalidGrantTypes.length > 0) {
      res.status(StatusCodes.BAD_REQUEST).json({
        error: 'invalid_request',
        message: `Invalid grant types: ${invalidGrantTypes.join(', ')}`,
      })
      return
    }

    // Validate redirect URIs (basic URL validation)
    if (!Array.isArray(redirect_uris) || redirect_uris.length === 0) {
      res.status(StatusCodes.BAD_REQUEST).json({
        error: 'invalid_request',
        message: 'At least one redirect URI is required',
      })
      return
    }

    for (const uri of redirect_uris) {
      try {
        new URL(uri)
      } catch {
        res.status(StatusCodes.BAD_REQUEST).json({
          error: 'invalid_request',
          message: `Invalid redirect URI: ${uri}`,
        })
        return
      }
    }

    // Validate webhook events if provided
    if (webhook_events && !Array.isArray(webhook_events)) {
      res.status(StatusCodes.BAD_REQUEST).json({
        error: 'invalid_request',
        message: 'webhook_events must be an array',
      })
      return
    }

    // Validate webhook URL if provided
    if (webhook_url) {
      try {
        new URL(webhook_url)
      } catch {
        res.status(StatusCodes.BAD_REQUEST).json({
          error: 'invalid_request',
          message: 'Invalid webhook URL',
        })
        return
      }
    }

    const client = await oauthClientService.createClient({
      client_name,
      redirect_uris,
      grant_types,
      scope,
      is_active,
      webhook_url,
      webhook_secret,
      webhook_events,
    })

    res.status(StatusCodes.CREATED).json(client)
  } catch (error) {
    console.error('Error creating client:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      error: 'server_error',
      message: 'Failed to create client',
    })
  }
}

export const updateClient = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const id = parseInt(req.params.id)
    if (isNaN(id)) {
      res.status(StatusCodes.BAD_REQUEST).json({
        error: 'invalid_request',
        message: 'Invalid client ID',
      })
      return
    }

    const {
      client_name,
      redirect_uris,
      grant_types,
      scope,
      is_active,
      webhook_url,
      webhook_secret,
      webhook_events,
    } = req.body

    // Check if client exists
    const existingClient = await oauthClientService.getClientById(id)
    if (!existingClient) {
      res.status(StatusCodes.NOT_FOUND).json({
        error: 'not_found',
        message: 'Client not found',
      })
      return
    }

    // Validate grant types if provided
    if (grant_types) {
      const validGrantTypes = [
        'authorization_code',
        'password',
        'refresh_token',
        'client_credentials',
      ]
      const invalidGrantTypes = grant_types.filter(
        (type: string) => !validGrantTypes.includes(type)
      )
      if (invalidGrantTypes.length > 0) {
        res.status(StatusCodes.BAD_REQUEST).json({
          error: 'invalid_request',
          message: `Invalid grant types: ${invalidGrantTypes.join(', ')}`,
        })
        return
      }
    }

    // Validate redirect URIs if provided
    if (redirect_uris) {
      if (!Array.isArray(redirect_uris) || redirect_uris.length === 0) {
        res.status(StatusCodes.BAD_REQUEST).json({
          error: 'invalid_request',
          message: 'At least one redirect URI is required',
        })
        return
      }

      for (const uri of redirect_uris) {
        try {
          new URL(uri)
        } catch {
          res.status(StatusCodes.BAD_REQUEST).json({
            error: 'invalid_request',
            message: `Invalid redirect URI: ${uri}`,
          })
          return
        }
      }
    }

    // Validate webhook events if provided
    if (webhook_events && !Array.isArray(webhook_events)) {
      res.status(StatusCodes.BAD_REQUEST).json({
        error: 'invalid_request',
        message: 'webhook_events must be an array',
      })
      return
    }

    // Validate webhook URL if provided
    if (webhook_url) {
      try {
        new URL(webhook_url)
      } catch {
        res.status(StatusCodes.BAD_REQUEST).json({
          error: 'invalid_request',
          message: 'Invalid webhook URL',
        })
        return
      }
    }

    const updatedClient = await oauthClientService.updateClient(id, {
      client_name,
      redirect_uris,
      grant_types,
      scope,
      is_active,
      webhook_url,
      webhook_secret,
      webhook_events,
    })

    if (!updatedClient) {
      res.status(StatusCodes.NOT_FOUND).json({
        error: 'not_found',
        message: 'Client not found',
      })
      return
    }

    res.json(updatedClient)
  } catch (error) {
    console.error('Error updating client:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      error: 'server_error',
      message: 'Failed to update client',
    })
  }
}

export const deleteClient = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const id = parseInt(req.params.id)
    if (isNaN(id)) {
      res.status(StatusCodes.BAD_REQUEST).json({
        error: 'invalid_request',
        message: 'Invalid client ID',
      })
      return
    }

    const success = await oauthClientService.deleteClient(id)
    if (!success) {
      res.status(StatusCodes.NOT_FOUND).json({
        error: 'not_found',
        message: 'Client not found',
      })
      return
    }

    res.status(StatusCodes.NO_CONTENT).send()
  } catch (error) {
    console.error('Error deleting client:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      error: 'server_error',
      message: 'Failed to delete client',
    })
  }
}

// Client-side OAuth token exchange endpoint
// This is where the client application backend exchanges the authorization code for tokens
// The client_secret is kept secure on the server side
export const exchangeToken = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { code, redirect_uri } = req.body

    if (!code || !redirect_uri) {
      res.status(StatusCodes.BAD_REQUEST).json({
        error: 'invalid_request',
        error_description: 'Missing authorization code or redirect_uri',
      })
      return
    }

    // OAuth client configuration (this should be in environment variables)
    const CLIENT_ID = 'varpro-client'
    const CLIENT_SECRET = 'varpro-client-secret' // This should be hashed and stored securely
    const OAUTH_SERVER_URL =
      process.env.OAUTH_SERVER_URL || 'http://localhost:3230'

    try {
      // Exchange authorization code for tokens with the OAuth server
      const tokenResponse = await axios.post(
        `${OAUTH_SERVER_URL}/oauth/token`,
        {
          grant_type: 'authorization_code',
          code: code,
          redirect_uri: redirect_uri,
          client_id: CLIENT_ID,
          client_secret: CLIENT_SECRET,
        },
        {
          headers: {
            'Content-Type': 'application/json',
          },
          timeout: 10000, // 10 second timeout
        }
      )

      const tokenData = tokenResponse.data

      // Return the tokens to the frontend
      res.json({
        access_token: tokenData.access_token,
        token_type: tokenData.token_type || 'Bearer',
        expires_in: tokenData.expires_in,
        refresh_token: tokenData.refresh_token,
        scope: tokenData.scope,
      })
    } catch (oauthError) {
      if (oauthError instanceof AxiosError && oauthError.response?.data) {
        // Forward OAuth server error to client
        res.status(StatusCodes.BAD_REQUEST).json({
          error: oauthError.response.data.error || 'token_exchange_failed',
          error_description:
            oauthError.response.data.error_description ||
            'Failed to exchange authorization code for tokens',
        })
      } else {
        res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
          error: 'server_error',
          error_description: 'Failed to communicate with OAuth server',
        })
      }
    }
  } catch (error) {
    console.error('Token exchange error:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      error: 'server_error',
      error_description: 'Internal server error',
    })
  }
}

import dotenv from 'dotenv'
import { afterAll, afterEach, beforeAll, beforeEach } from 'vitest'
import { db } from '../db/database'

// Load environment variables for testing
dotenv.config()

// Global test setup
beforeAll(async () => {
  console.log('🧪 Setting up test environment...')

  // Ensure we're using a test database
  if (!process.env.DATABASE_URL?.includes('test')) {
    console.log(
      '⚠️  Warning: Not using a test database. Consider setting DATABASE_URL to a test database.'
    )
  }
})

// Global test teardown
afterAll(async () => {
  console.log('🧹 Cleaning up test environment...')

  try {
    // Close database connections
    await db.destroy()
  } catch (error) {
    console.error('Error during test cleanup:', error)
  }
})

// Setup before each test
beforeEach(async () => {
  // Any per-test setup can go here
})

// Cleanup after each test
afterEach(async () => {
  // Any per-test cleanup can go here
})

// Helper function to create test employee
export async function createTestEmployee(overrides: any = {}) {
  // Generate a unique identifier for test employees
  const timestamp = Date.now()
  const random = Math.floor(Math.random() * 1000)

  const defaultEmployee = {
    status: 1,
    first_name: 'Test',
    last_name: 'User',
    department: 'IT',
    employment_date: new Date().toISOString().split('T')[0],
    email: `test.user.${timestamp}.${random}@test.com`, // Ensure unique email
    ...overrides,
  }

  // MySQL doesn't support RETURNING, so we need to insert and then get the ID
  const result = await db
    .insertInto('employees')
    .values(defaultEmployee)
    .execute()

  // Get the last inserted ID
  const insertId = result[0].insertId
  if (!insertId || insertId === 0n) {
    throw new Error('Failed to create test employee: insertId is 0 or null')
  }
  return Number(insertId)
}

// Helper function to cleanup test employees
export async function cleanupTestEmployees() {
  await db.deleteFrom('employees').where('first_name', '=', 'Test').execute()
}

// Helper function to create test OAuth client
export async function createTestOAuthClient(overrides: any = {}) {
  // Generate unique identifiers for test clients
  const timestamp = Date.now()
  const random = Math.floor(Math.random() * 1000)

  const defaultClient = {
    client_name: 'Test Client',
    client_id: `test-client-${timestamp}-${random}`,
    client_secret: `test-secret-${timestamp}-${random}`,
    redirect_uris: JSON.stringify(['http://localhost:3000/callback']),
    grant_types: JSON.stringify(['authorization_code', 'refresh_token']),
    scope: 'read write',
    is_active: 1, // MySQL uses 1/0 for boolean
    ...overrides,
  }

  // MySQL doesn't support RETURNING, so we need to insert and then get the ID
  const result = await db
    .insertInto('oauth_clients')
    .values(defaultClient)
    .execute()

  // Get the last inserted ID
  const insertId = result[0].insertId
  if (!insertId || insertId === 0n) {
    throw new Error('Failed to create test OAuth client: insertId is 0 or null')
  }
  return Number(insertId)
}

// Helper function to cleanup test OAuth clients
export async function cleanupTestOAuthClients() {
  await db
    .deleteFrom('oauth_clients')
    .where('client_name', '=', 'Test Client')
    .execute()
}

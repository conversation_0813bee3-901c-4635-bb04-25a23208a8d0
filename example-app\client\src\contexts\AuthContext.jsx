import React, { createContext, useContext, useState, useEffect } from 'react'
import oauthService from '../services/oauthService.js'

const AuthContext = createContext()

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  // Initialize auth state on app load
  useEffect(() => {
    initializeAuth()
  }, [])

  const initializeAuth = async () => {
    try {
      setLoading(true)
      setError(null)

      // Check if user is already authenticated
      if (oauthService.isAuthenticated()) {
        const userInfo = oauthService.getUserInfoFromStorage()
        if (userInfo) {
          setUser(userInfo)
        } else {
          // Try to fetch user info if not in storage
          try {
            const freshUserInfo = await oauthService.getUserInfo()
            setUser(freshUserInfo)
          } catch (err) {
            console.error('Failed to get user info:', err)
            // Clear invalid tokens
            oauthService.clearTokens()
          }
        }
      }
    } catch (err) {
      console.error('Auth initialization failed:', err)
      setError('Failed to initialize authentication')
    } finally {
      setLoading(false)
    }
  }

  const login = async (username, password) => {
    try {
      setLoading(true)
      setError(null)

      const result = await oauthService.loginWithPassword(username, password)
      
      if (result.success) {
        setUser(result.userInfo)
        return { success: true }
      } else {
        setError(result.error)
        return { success: false, error: result.error }
      }
    } catch (err) {
      const errorMessage = 'Login failed. Please try again.'
      setError(errorMessage)
      return { success: false, error: errorMessage }
    } finally {
      setLoading(false)
    }
  }

  const loginWithAuthCode = () => {
    oauthService.initiateAuthorizationCodeFlow()
  }

  const handleAuthCallback = async (code, state) => {
    try {
      setLoading(true)
      setError(null)

      const result = await oauthService.handleAuthorizationCallback(code, state)
      
      if (result.success) {
        setUser(result.userInfo)
        return { success: true }
      } else {
        setError(result.error)
        return { success: false, error: result.error }
      }
    } catch (err) {
      const errorMessage = 'Authorization failed. Please try again.'
      setError(errorMessage)
      return { success: false, error: errorMessage }
    } finally {
      setLoading(false)
    }
  }

  const logout = async () => {
    try {
      setLoading(true)
      await oauthService.logout()
      setUser(null)
      setError(null)
    } catch (err) {
      console.error('Logout failed:', err)
      // Clear local state even if server logout fails
      setUser(null)
      setError(null)
    } finally {
      setLoading(false)
    }
  }

  const refreshUserInfo = async () => {
    try {
      const userInfo = await oauthService.getUserInfo()
      setUser(userInfo)
      return userInfo
    } catch (err) {
      console.error('Failed to refresh user info:', err)
      throw err
    }
  }

  const clearError = () => {
    setError(null)
  }

  const value = {
    user,
    loading,
    error,
    isAuthenticated: !!user,
    login,
    loginWithAuthCode,
    handleAuthCallback,
    logout,
    refreshUserInfo,
    clearError
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

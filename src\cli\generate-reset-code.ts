#!/usr/bin/env node

import { employeeService } from '../services/employeeService'

async function generateResetCode() {
  const args = process.argv.slice(2)
  
  if (args.length === 0) {
    console.log('Usage: npm run cli:reset-code <employee_id>')
    console.log('Example: npm run cli:reset-code 123')
    process.exit(1)
  }

  const employeeId = parseInt(args[0])
  
  if (isNaN(employeeId) || employeeId <= 0) {
    console.error('❌ Error: Please provide a valid employee ID (positive number)')
    process.exit(1)
  }

  try {
    console.log(`🔄 Generating password reset code for employee ID: ${employeeId}`)
    
    const result = await employeeService.generatePasswordResetCode(employeeId)
    
    if (!result.success) {
      console.error(`❌ Error: ${result.error}`)
      process.exit(1)
    }

    const baseUrl = process.env.BASE_URL || 'http://localhost:3000'
    const resetUrl = `${baseUrl}/reset-password.html?employee_id=${employeeId}&code=${result.code}`
    
    console.log('\n✅ Password reset code generated successfully!')
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
    console.log(`📋 Employee ID: ${employeeId}`)
    console.log(`🔑 Reset Code:  ${result.code}`)
    console.log(`⏰ Expires:     10 minutes from now`)
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
    console.log('\n🔗 Reset Link:')
    console.log(resetUrl)
    console.log('\n📝 Instructions:')
    console.log('1. Send this link to the employee via secure communication')
    console.log('2. The employee can click the link to reset their password')
    console.log('3. The form will be pre-filled with their employee ID and reset code')
    console.log('4. The code expires in 10 minutes for security')
    console.log('\n⚠️  Security Notes:')
    console.log('• This code can only be used with the specified employee ID')
    console.log('• The code expires automatically after 10 minutes')
    console.log('• Only one active reset code per employee (previous codes are invalidated)')
    console.log('• The code can only be used once')
    
  } catch (error) {
    console.error('❌ Error generating reset code:', error)
    process.exit(1)
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n👋 Goodbye!')
  process.exit(0)
})

process.on('SIGTERM', () => {
  console.log('\n👋 Goodbye!')
  process.exit(0)
})

// Run the CLI
generateResetCode().catch((error) => {
  console.error('❌ Unexpected error:', error)
  process.exit(1)
})

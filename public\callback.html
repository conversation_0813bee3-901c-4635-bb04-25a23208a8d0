<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>OAuth Callback - VarPro</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family:
          -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu,
          Cantarell, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px;
      }

      .callback-container {
        background: white;
        border-radius: 12px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        padding: 40px;
        width: 100%;
        max-width: 500px;
        text-align: center;
      }

      .logo {
        margin-bottom: 30px;
      }

      .logo h1 {
        color: #333;
        font-size: 28px;
        font-weight: 700;
        margin-bottom: 8px;
      }

      .logo p {
        color: #666;
        font-size: 14px;
      }

      .status {
        margin-bottom: 30px;
      }

      .status.loading {
        color: #667eea;
      }

      .status.success {
        color: #28a745;
      }

      .status.error {
        color: #dc3545;
      }

      .spinner {
        display: inline-block;
        width: 32px;
        height: 32px;
        border: 3px solid #f3f3f3;
        border-top: 3px solid #667eea;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 16px;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      .message {
        font-size: 16px;
        margin-bottom: 20px;
      }

      .details {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 16px;
        margin-bottom: 20px;
        text-align: left;
      }

      .details h4 {
        color: #333;
        margin-bottom: 12px;
      }

      .details p {
        color: #666;
        font-size: 14px;
        margin-bottom: 8px;
        word-break: break-all;
      }

      .btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        text-decoration: none;
        display: inline-block;
        margin: 8px;
      }

      .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
      }

      .btn.secondary {
        background: #6c757d;
      }

      .error-details {
        background: #fee;
        border: 1px solid #fed7d7;
        color: #c53030;
        padding: 16px;
        border-radius: 8px;
        margin-bottom: 20px;
        text-align: left;
      }
    </style>
  </head>
  <body>
    <div class="callback-container">
      <div class="logo">
        <h1>VarPro</h1>
        <p>OAuth Authentication</p>
      </div>

      <div id="loading" class="status loading">
        <div class="spinner"></div>
        <div class="message">Processing authorization...</div>
      </div>

      <div id="success" class="status success" style="display: none">
        <div class="message">✅ Authentication successful!</div>
        <div class="details">
          <h4>Token Information</h4>
          <p><strong>Access Token:</strong> <span id="accessToken">-</span></p>
          <p><strong>Token Type:</strong> <span id="tokenType">-</span></p>
          <p>
            <strong>Expires In:</strong> <span id="expiresIn">-</span> seconds
          </p>
          <p><strong>Scope:</strong> <span id="scope">-</span></p>
        </div>
        <a href="/dashboard.html" class="btn">Go to Dashboard</a>
        <a href="/" class="btn secondary">Home</a>
      </div>

      <div id="error" class="status error" style="display: none">
        <div class="message">❌ Authentication failed</div>
        <div class="error-details">
          <h4>Error Details</h4>
          <p><strong>Error:</strong> <span id="errorType">-</span></p>
          <p>
            <strong>Description:</strong> <span id="errorDescription">-</span>
          </p>
        </div>
        <a href="/" class="btn">Try Again</a>
      </div>
    </div>

    <script>
      // Parse URL parameters
      const urlParams = new URLSearchParams(window.location.search)
      const code = urlParams.get('code')
      const state = urlParams.get('state')
      const error = urlParams.get('error')
      const errorDescription = urlParams.get('error_description')

      // Check if there's an error in the callback
      if (error) {
        showError(error, errorDescription || 'Unknown error occurred')
        return
      }

      // Validate state parameter (if we stored one)
      const storedState = localStorage.getItem('oauth_state')
      if (storedState && state !== storedState) {
        showError(
          'invalid_state',
          'State parameter mismatch - possible CSRF attack'
        )
        return
      }

      // Clear stored state
      localStorage.removeItem('oauth_state')

      // Exchange authorization code for tokens
      if (code) {
        exchangeCodeForTokens(code)
      } else {
        showError('missing_code', 'No authorization code received')
      }

      async function exchangeCodeForTokens(authCode) {
        try {
          // Use the backend endpoint to exchange the code for tokens
          // This keeps the client_secret secure on the server side
          const response = await fetch('/api/v1/clients/exchange-token', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              code: authCode,
              redirect_uri: window.location.origin + '/callback.html',
            }),
          })

          const data = await response.json()

          if (!response.ok) {
            throw new Error(
              data.error_description || data.message || 'Token exchange failed'
            )
          }

          // Store tokens securely (in a real app, use secure storage)
          localStorage.setItem('access_token', data.access_token)
          if (data.refresh_token) {
            localStorage.setItem('refresh_token', data.refresh_token)
          }

          // Show success
          showSuccess(data)
        } catch (error) {
          console.error('Token exchange error:', error)
          showError('token_exchange_failed', error.message)
        }
      }

      function showSuccess(tokenData) {
        document.getElementById('loading').style.display = 'none'
        document.getElementById('success').style.display = 'block'

        document.getElementById('accessToken').textContent =
          tokenData.access_token.substring(0, 20) + '...'
        document.getElementById('tokenType').textContent =
          tokenData.token_type || 'Bearer'
        document.getElementById('expiresIn').textContent =
          tokenData.expires_in || 'Unknown'
        document.getElementById('scope').textContent =
          tokenData.scope || 'Default'
      }

      function showError(errorType, description) {
        document.getElementById('loading').style.display = 'none'
        document.getElementById('error').style.display = 'block'

        document.getElementById('errorType').textContent = errorType
        document.getElementById('errorDescription').textContent = description
      }
    </script>
  </body>
</html>

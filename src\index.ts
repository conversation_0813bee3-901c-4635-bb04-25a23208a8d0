import cors from 'cors'
import 'dotenv/config'
import express, { Express, Request, Response } from 'express'
import path from 'path'
import { api<PERSON>outer } from './routes/api'
import { oAuthRouter } from './routes/oauth'
import { wellKnownRouter } from './routes/wellKnown'

export const server = () => {
  const app: Express = express()
  const port = process.env.PORT || 3000

  // Enable CORS with environment-based configuration
  app.use(
    cors({
      origin: (origin, callback) => {
        // Allow requests with no origin (like mobile apps or curl requests)
        if (!origin) return callback(null, true)

        // Get allowed origins from environment variable
        const corsOrigins = process.env.CORS_ORIGINS

        if (corsOrigins) {
          // Production or explicit CORS configuration
          const allowedOrigins = corsOrigins
            .split(',')
            .map((origin) => origin.trim())
          if (allowedOrigins.includes(origin)) {
            return callback(null, true)
          }
          return callback(new Error('Not allowed by CORS'))
        } else {
          // Development mode - allow any localhost port when CORS_ORIGINS is not set
          if (
            origin.startsWith('http://localhost:') ||
            origin.startsWith('http://127.0.0.1:')
          ) {
            return callback(null, true)
          }
          return callback(new Error('Not allowed by CORS'))
        }
      },
      credentials: true,
    })
  )

  // Parse JSON bodies
  app.use(express.json())
  app.use(express.urlencoded({ extended: true }))

  app.use('/api', apiRouter)
  app.use('/oauth', oAuthRouter)
  app.use('/.well-known', wellKnownRouter)

  // Serve static files from public directory (HTML login pages)
  app.use(express.static(path.join(__dirname, '../public')))

  // Serve admin React app at /admin
  app.use('/admin', express.static(path.join(__dirname, '../../client/dist')))

  // Default route serves the main index page
  app.get('/', (_req: Request, res: Response) => {
    res.sendFile(path.join(__dirname, '../public/index.html'))
  })

  app.listen(port, () => {
    console.log(`Server is running at http://localhost:${port}`)
  })
}
server()

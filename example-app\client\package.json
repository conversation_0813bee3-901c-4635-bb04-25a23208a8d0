{"name": "oauth-client-example", "version": "1.0.0", "description": "Example client application demonstrating OAuth 2.0 integration with RS256 JWT tokens", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0"}, "dependencies": {"axios": "^1.6.2", "jose": "^5.1.3", "jsonwebtoken": "^9.0.2", "jwt-decode": "^4.0.0", "node-fetch": "^3.3.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@vitejs/plugin-react": "^4.1.1", "eslint": "^8.53.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "vite": "^5.0.0"}, "keywords": ["oauth2", "jwt", "rs256", "authentication", "react", "example"], "author": "OAuth Example", "license": "MIT"}
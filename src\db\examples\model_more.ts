import { ColumnType, Generated, JSONColumnType } from 'kysely'

export interface UserTable {
  id: Generated<number>

  username: string | null
  name: string
  employee_id: number | null
  email: string | null
  password: string
  remember_token: string | null
  confirmed: boolean
  is_admin: boolean
  created_at: ColumnType<Date, string | undefined, never>
  updated_at: ColumnType<Date, string | undefined, string | undefined>
  api_key: string | null
  webhook_url: string | null
}

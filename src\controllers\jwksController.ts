import { createP<PERSON><PERSON><PERSON><PERSON> } from 'crypto'
import { Request, Response } from 'express'
import { StatusCodes } from 'http-status-codes'
import { oauthTokenService } from '../services/oauthTokenService'

/**
 * J<PERSON><PERSON> (JSON Web Key Set) endpoint for RS256 public key distribution
 * This follows the RFC 7517 standard for JSON Web Key Sets
 */
export const getJWKS = async (req: Request, res: Response): Promise<void> => {
  try {
    const algorithm = oauthTokenService.getAlgorithm()

    if (algorithm !== 'RS256') {
      res.status(StatusCodes.NOT_IMPLEMENTED).json({
        error: 'not_supported',
        error_description:
          'JW<PERSON> endpoint is only available for RS256 algorithm',
      })
      return
    }

    const publicKey = oauthTokenService.getPublicKey()
    if (!publicKey) {
      res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        error: 'server_error',
        error_description: 'Public key not available',
      })
      return
    }

    // Convert PEM public key to JWK format
    const keyObject = createPublicKey(publicKey)
    const jwk = keyObject.export({ format: 'jwk' })

    // Add required JWK fields
    jwk.use = 'sig' // This key is used for signature verification
    jwk.alg = 'RS256' // Algorithm
    jwk.kid = 'varpro-oauth2-server-key-1' // Key ID

    const jwks = {
      keys: [jwk],
    }

    // Set appropriate headers
    res.setHeader('Content-Type', 'application/json')
    res.setHeader('Cache-Control', 'public, max-age=3600') // Cache for 1 hour

    res.json(jwks)
  } catch (error) {
    console.error('Error generating JWKS:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      error: 'server_error',
      error_description: 'Failed to generate JWKS',
    })
  }
}

/**
 * Simple public key endpoint (PEM format)
 */
export const getPublicKey = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const algorithm = oauthTokenService.getAlgorithm()

    if (algorithm !== 'RS256') {
      res.status(StatusCodes.NOT_IMPLEMENTED).json({
        error: 'not_supported',
        error_description:
          'Public key endpoint is only available for RS256 algorithm',
      })
      return
    }

    const publicKey = oauthTokenService.getPublicKey()
    if (!publicKey) {
      res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        error: 'server_error',
        error_description: 'Public key not available',
      })
      return
    }

    // Set appropriate headers
    res.setHeader('Content-Type', 'application/x-pem-file')
    res.setHeader('Cache-Control', 'public, max-age=3600') // Cache for 1 hour

    res.send(publicKey)
  } catch (error) {
    console.error('Error getting public key:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      error: 'server_error',
      error_description: 'Failed to get public key',
    })
  }
}

/**
 * OAuth server metadata endpoint (RFC 8414)
 */
export const getServerMetadata = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const baseUrl = `${req.protocol}://${req.get('host')}`

    const metadata = {
      issuer: 'varpro-oauth2-server',
      authorization_endpoint: `${baseUrl}/oauth/authorize`,
      token_endpoint: `${baseUrl}/oauth/token`,
      revocation_endpoint: `${baseUrl}/oauth/revoke`,
      jwks_uri: `${baseUrl}/.well-known/jwks.json`,
      response_types_supported: ['code'],
      grant_types_supported: [
        'authorization_code',
        'password',
        'refresh_token',
      ],
      token_endpoint_auth_methods_supported: [
        'client_secret_post',
        'client_secret_basic',
      ],
      scopes_supported: ['read', 'write'],
      id_token_signing_alg_values_supported: [oauthTokenService.getAlgorithm()],
      subject_types_supported: ['public'],
      claims_supported: [
        'sub',
        'iat',
        'exp',
        'aud',
        'iss',
        'client_id',
        'employee_id',
      ],
    }

    // Set appropriate headers
    res.setHeader('Content-Type', 'application/json')
    res.setHeader('Cache-Control', 'public, max-age=3600') // Cache for 1 hour

    res.json(metadata)
  } catch (error) {
    console.error('Error generating server metadata:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      error: 'server_error',
      error_description: 'Failed to generate server metadata',
    })
  }
}

import React, { useState, useEffect } from 'react'
import oauthService from '../services/oauthService.js'

const TokenPage = () => {
  const [accessToken, setAccessToken] = useState('')
  const [refreshToken, setRefreshToken] = useState('')
  const [decodedToken, setDecodedToken] = useState(null)
  const [tokenVerification, setTokenVerification] = useState(null)
  const [jwks, setJwks] = useState(null)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    loadTokenData()
  }, [])

  const loadTokenData = async () => {
    try {
      setLoading(true)

      // Get current tokens
      const currentAccessToken = oauthService.getAccessToken()
      const currentRefreshToken = localStorage.getItem('oauth_refresh_token')
      
      setAccessToken(currentAccessToken || '')
      setRefreshToken(currentRefreshToken || '')

      if (currentAccessToken) {
        // Decode token
        const decoded = oauthService.decodeToken(currentAccessToken)
        setDecodedToken(decoded)

        // Verify token
        const verification = await oauthService.verifyToken(currentAccessToken)
        setTokenVerification(verification)
      }

      // Get JWKS
      const jwksData = await oauthService.getJWKS()
      setJwks(jwksData)

    } catch (error) {
      console.error('Failed to load token data:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleRefreshToken = async () => {
    try {
      setLoading(true)
      const success = await oauthService.refreshToken()
      if (success) {
        await loadTokenData()
        alert('Token refreshed successfully!')
      } else {
        alert('Token refresh failed')
      }
    } catch (error) {
      console.error('Token refresh error:', error)
      alert('Token refresh failed')
    } finally {
      setLoading(false)
    }
  }

  const formatDate = (timestamp) => {
    return new Date(timestamp * 1000).toLocaleString()
  }

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text)
    alert('Copied to clipboard!')
  }

  if (loading && !accessToken) {
    return (
      <div className="loading">
        <div className="spinner"></div>
        <p className="mt-2">Loading token information...</p>
      </div>
    )
  }

  return (
    <div className="container">
      <div className="card">
        <h1>🔐 JWT Token Analysis</h1>
        <p className="text-muted">
          Detailed analysis of your current OAuth 2.0 JWT tokens with RS256 verification.
        </p>
      </div>

      {/* Token Actions */}
      <div className="card">
        <h2>Token Actions</h2>
        <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
          <button 
            onClick={loadTokenData} 
            className="btn"
            disabled={loading}
          >
            {loading ? 'Refreshing...' : 'Refresh Data'}
          </button>
          <button 
            onClick={handleRefreshToken} 
            className="btn btn-secondary"
            disabled={loading || !refreshToken}
          >
            Refresh Access Token
          </button>
        </div>
      </div>

      <div className="grid grid-2">
        {/* Access Token */}
        <div className="card">
          <h2>Access Token</h2>
          {accessToken ? (
            <div>
              <div className="form-group">
                <label className="form-label">Token (First 100 chars)</label>
                <div className="code-block" style={{ fontSize: '12px' }}>
                  {accessToken.substring(0, 100)}...
                </div>
                <button 
                  onClick={() => copyToClipboard(accessToken)}
                  className="btn btn-secondary mt-1"
                  style={{ fontSize: '12px' }}
                >
                  Copy Full Token
                </button>
              </div>

              {decodedToken && (
                <div>
                  <h3>Decoded Claims</h3>
                  <div className="code-block">
                    {JSON.stringify(decodedToken, null, 2)}
                  </div>
                  
                  <div className="mt-2">
                    <p><strong>Expires:</strong> {formatDate(decodedToken.exp)}</p>
                    <p><strong>Time until expiry:</strong> {
                      Math.max(0, Math.floor((decodedToken.exp * 1000 - Date.now()) / 1000 / 60))
                    } minutes</p>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <p>No access token available</p>
          )}
        </div>

        {/* Token Verification */}
        <div className="card">
          <h2>Token Verification</h2>
          {tokenVerification ? (
            <div>
              <div className={`alert ${tokenVerification.valid ? 'alert-success' : 'alert-error'}`}>
                <strong>Signature Status:</strong> {tokenVerification.valid ? '✅ Valid' : '❌ Invalid'}
              </div>
              
              {tokenVerification.valid ? (
                <div>
                  <h3>Verified Claims</h3>
                  <div className="code-block">
                    {JSON.stringify(tokenVerification.payload, null, 2)}
                  </div>
                  <p className="mt-2 text-muted">
                    Token signature verified using RS256 public key from JWKS endpoint.
                  </p>
                </div>
              ) : (
                <div>
                  <p><strong>Error:</strong> {tokenVerification.error}</p>
                  <p className="text-muted">
                    Token signature could not be verified. This could indicate:
                  </p>
                  <ul>
                    <li>Token has been tampered with</li>
                    <li>Token has expired</li>
                    <li>Wrong public key used for verification</li>
                    <li>Network error accessing JWKS endpoint</li>
                  </ul>
                </div>
              )}
            </div>
          ) : (
            <p>No verification data available</p>
          )}
        </div>
      </div>

      {/* Refresh Token */}
      <div className="card">
        <h2>Refresh Token</h2>
        {refreshToken ? (
          <div>
            <div className="form-group">
              <label className="form-label">Refresh Token (First 50 chars)</label>
              <div className="code-block" style={{ fontSize: '12px' }}>
                {refreshToken.substring(0, 50)}...
              </div>
              <button 
                onClick={() => copyToClipboard(refreshToken)}
                className="btn btn-secondary mt-1"
                style={{ fontSize: '12px' }}
              >
                Copy Full Token
              </button>
            </div>
            <p className="text-muted">
              Refresh tokens are opaque strings used to obtain new access tokens without re-authentication.
            </p>
          </div>
        ) : (
          <p>No refresh token available</p>
        )}
      </div>

      {/* JWKS Information */}
      <div className="card">
        <h2>JWKS (JSON Web Key Set)</h2>
        {jwks ? (
          <div>
            <p>
              <strong>Number of keys:</strong> {jwks.keys?.length || 0}
            </p>
            {jwks.keys?.map((key, index) => (
              <div key={index} className="mt-2">
                <h3>Key {index + 1}</h3>
                <p><strong>Algorithm:</strong> {key.alg}</p>
                <p><strong>Key Type:</strong> {key.kty}</p>
                <p><strong>Use:</strong> {key.use}</p>
                <p><strong>Key ID:</strong> {key.kid}</p>
                
                <div className="form-group">
                  <label className="form-label">Public Key (JWK Format)</label>
                  <div className="code-block">
                    {JSON.stringify(key, null, 2)}
                  </div>
                </div>
              </div>
            ))}
            
            <div className="alert alert-info mt-3">
              <strong>How RS256 Verification Works:</strong>
              <ol>
                <li>Client receives JWT token from OAuth server</li>
                <li>Client fetches public key from JWKS endpoint</li>
                <li>Client verifies token signature using public key</li>
                <li>If signature is valid, token claims are trusted</li>
              </ol>
              <p className="mb-0">
                This allows distributed verification without sharing secrets!
              </p>
            </div>
          </div>
        ) : (
          <p>Failed to load JWKS data</p>
        )}
      </div>
    </div>
  )
}

export default TokenPage

import { NextFunction, Request, Response } from 'express'
import { StatusCodes } from 'http-status-codes'
import { oauthTokenService, TokenPayload } from '../services/oauthTokenService'

// Extend Request interface to include tokenPayload
export interface AuthenticatedReponse extends Response {
  locals: {
    tokenPayload: TokenPayload
    accessToken: string
    employee: TokenPayload['employee']
  }
}

/**
 * JWT Authentication Middleware for OAuth Server
 * Validates JWT tokens and adds payload to request
 */
export const jwtHeaderAuthMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // Set cache control headers
    res.set({
      'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
      Pragma: 'no-cache',
      Expires: '0',
      'Surrogate-Control': 'no-store',
    })

    // Extract token from Authorization header
    const authHeader = req.headers.authorization
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        error: 'unauthorized',
        error_description: 'Missing or invalid Authorization header',
      })
      return
    }

    const token = authHeader.substring(7)

    // Validate the token using the OAuth token service
    const tokenPayload = await oauthTokenService.validateAccessToken(token)

    if (!tokenPayload) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        error: 'invalid_token',
        error_description: 'Token validation failed',
      })
      return
    }

    // Add token payload and token to request
    res.locals.tokenPayload = tokenPayload
    res.locals.accessToken = token
    res.locals.employee = tokenPayload.employee

    next()
  } catch (error) {
    console.error('JWT authentication middleware error:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      error: 'server_error',
      error_description: 'Authentication failed',
    })
  }
}

export const jwtCookieAuthMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // Set cache control headers
    res.set({
      'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
      Pragma: 'no-cache',
      Expires: '0',
      'Surrogate-Control': 'no-store',
    })

    // Extract token from cookie
    const token = req.cookies?.access_token
    if (!token) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        error: 'unauthorized',
        error_description: 'Missing or invalid access_token cookie',
      })
      return
    }

    // Validate the token using the OAuth token service
    const tokenPayload = await oauthTokenService.validateAccessToken(token)

    if (!tokenPayload) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        error: 'invalid_token',
        error_description: 'Token validation failed',
      })
      return
    }

    // Add token payload and token to request
    res.locals.tokenPayload = tokenPayload
    res.locals.accessToken = token
    res.locals.employee = tokenPayload.employee

    next()
  } catch (error) {
    console.error('JWT cookie authentication middleware error:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      error: 'server_error',
      error_description: 'Authentication failed',
    })
  }
}

export const jwtAdminAuthMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  const tokenPayload = (res.locals as AuthenticatedReponse).locals.tokenPayload
  if (!tokenPayload || !tokenPayload.employee?.is_admin) {
    res.status(StatusCodes.FORBIDDEN).json({
      error: 'forbidden',
      error_description: 'Admin access required',
    })
    return
  }
  next()
}

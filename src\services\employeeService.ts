import * as bcrypt from 'bcrypt'
import { v4 as uuidv4 } from 'uuid'
import { db } from '../db/database'
import { Employee, EmployeeUpdate } from '../db/models/employees'

interface EmployeeResponse {
  employee_id: number
  status: number
  first_name: string
  last_name: string
  department: string
  short_name: string | null
  nickname: string | null
  email: string | null
  work_area_id: number | null
  employment_date: string | Date
  image: string | null
  employee_contact: string | null
  emergency_number: string | null
  gender: string | null
  barcode: number
  created_at: string | Date
  updated_at: string | Date
  emp_barcode: number | null
  rfid_code: number | null
  section: string | null
  title: string | null
  departments_id: number | null
  title_id: number | null
  username: string | null
  last_login_at: string | Date | null
  failed_login_attempts: number
  locked_until: string | Date | null
  pin: string | null
}

export class EmployeeService {
  private readonly saltRounds = 12

  /**
   * Validates username according to business rules:
   * - Must have at least 1 alphabetic character
   * - Cannot contain @ character
   */
  private validateUsername(username: string): {
    isValid: boolean
    error?: string
  } {
    if (!username || username.trim().length === 0) {
      return { isValid: false, error: 'Username cannot be empty' }
    }

    // Check if username contains @ character
    if (username.includes('@')) {
      return { isValid: false, error: 'Username cannot contain @ character' }
    }

    // Check if username has at least 1 alphabetic character
    const hasAlphaChar = /[a-zA-Z]/.test(username)
    if (!hasAlphaChar) {
      return {
        isValid: false,
        error: 'Username must contain at least 1 alphabetic character',
      }
    }

    return { isValid: true }
  }

  /**
   * Validates PIN according to business rules:
   * - Must be 4-8 numeric characters only
   */
  private validatePin(pin: string): {
    isValid: boolean
    error?: string
  } {
    if (!pin || pin.trim().length === 0) {
      return { isValid: false, error: 'PIN cannot be empty' }
    }

    // Check if PIN is only numeric
    const isNumeric = /^\d+$/.test(pin)
    if (!isNumeric) {
      return {
        isValid: false,
        error: 'PIN must contain only numeric characters',
      }
    }

    // Check PIN length (4-8 digits)
    if (pin.length < 4 || pin.length > 8) {
      return { isValid: false, error: 'PIN must be 4-8 digits long' }
    }

    return { isValid: true }
  }

  /**
   * Determines if a password is actually a PIN (all numeric, 4-8 characters)
   */
  private isPinPassword(password: string): boolean {
    return /^\d{4,8}$/.test(password)
  }

  async getAllEmployees(): Promise<EmployeeResponse[]> {
    const employees = await db
      .selectFrom('employees')
      .select([
        'employee_id',
        'status',
        'first_name',
        'last_name',
        'department',
        'short_name',
        'nickname',
        'email',
        'work_area_id',
        'employment_date',
        'image',
        'employee_contact',
        'emergency_number',
        'gender',
        'barcode',
        'created_at',
        'updated_at',
        'emp_barcode',
        'rfid_code',
        'section',
        'title',
        'departments_id',
        'title_id',
        'username',
        'last_login_at',
        'failed_login_attempts',
        'locked_until',
        'pin',
      ])
      .where('status', '=', 1) // Only active employees
      .orderBy('created_at', 'desc')
      .execute()

    return employees
  }

  async getEmployeeById(
    employee_id: number
  ): Promise<EmployeeResponse | undefined> {
    const employee = await db
      .selectFrom('employees')
      .select([
        'employee_id',
        'status',
        'first_name',
        'last_name',
        'department',
        'short_name',
        'nickname',
        'email',
        'work_area_id',
        'employment_date',
        'image',
        'employee_contact',
        'emergency_number',
        'gender',
        'barcode',
        'created_at',
        'updated_at',
        'emp_barcode',
        'rfid_code',
        'section',
        'title',
        'departments_id',
        'title_id',
        'username',
        'last_login_at',
        'failed_login_attempts',
        'locked_until',
        'pin',
      ])
      .where('employee_id', '=', employee_id)
      .where('status', '=', 1)
      .executeTakeFirst()

    return employee
  }

  async getEmployeeByUsername(username: string): Promise<Employee | undefined> {
    return await db
      .selectFrom('employees')
      .selectAll()
      .where('username', '=', username)
      .where('status', '=', 1)
      .executeTakeFirst()
  }

  async getEmployeeByEmail(email: string): Promise<Employee | undefined> {
    return await db
      .selectFrom('employees')
      .selectAll()
      .where('email', '=', email)
      .where('status', '=', 1)
      .executeTakeFirst()
  }

  async updateEmployeePassword(
    employee_id: number,
    password: string
  ): Promise<boolean> {
    // Check if the password is actually a PIN
    if (this.isPinPassword(password)) {
      return await this.updateEmployeePin(employee_id, password)
    }

    // Validate that password has at least 1 alphabetic character
    const hasAlphaChar = /[a-zA-Z]/.test(password)
    if (!hasAlphaChar) {
      throw new Error('Password must contain at least 1 alphabetic character')
    }

    const passwordHash = await bcrypt.hash(password, this.saltRounds)

    const result = await db
      .updateTable('employees')
      .set({
        password_hash: passwordHash,
        pin: null, // Clear PIN when setting a password
        updated_at: new Date().toISOString(),
      })
      .where('employee_id', '=', employee_id)
      .executeTakeFirst()

    return result.numUpdatedRows > 0
  }

  async updateEmployeePin(employee_id: number, pin: string): Promise<boolean> {
    // Validate PIN format
    const validation = this.validatePin(pin)
    if (!validation.isValid) {
      throw new Error(validation.error)
    }

    const result = await db
      .updateTable('employees')
      .set({
        pin: pin,
        password_hash: null, // Clear password hash when setting a PIN
        updated_at: new Date().toISOString(),
      })
      .where('employee_id', '=', employee_id)
      .executeTakeFirst()

    return result.numUpdatedRows > 0
  }

  async updateEmployee(
    employee_id: number,
    updates: EmployeeUpdate
  ): Promise<EmployeeResponse | undefined> {
    const updateData: EmployeeUpdate = {
      ...updates,
      updated_at: new Date().toISOString(),
    }

    // Validate username if provided
    if (updates.username) {
      const validation = this.validateUsername(updates.username)
      if (!validation.isValid) {
        throw new Error(validation.error)
      }

      // Check if username already exists
      const existingEmployee = await this.getEmployeeByUsername(
        updates.username
      )
      if (existingEmployee && existingEmployee.employee_id !== employee_id) {
        throw new Error('Username already exists')
      }
    }

    // Hash password if provided
    if ('password' in updates && updates.password) {
      updateData.password_hash = await bcrypt.hash(
        updates.password as string,
        this.saltRounds
      )
      delete (updateData as EmployeeUpdate & { password?: string }).password // Remove plain password from update data
    }

    const result = await db
      .updateTable('employees')
      .set(updateData)
      .where('employee_id', '=', employee_id)
      .returning([
        'employee_id',
        'status',
        'first_name',
        'last_name',
        'department',
        'short_name',
        'nickname',
        'email',
        'work_area_id',
        'employment_date',
        'image',
        'employee_contact',
        'emergency_number',
        'gender',
        'barcode',
        'created_at',
        'updated_at',
        'emp_barcode',
        'rfid_code',
        'section',
        'title',
        'departments_id',
        'title_id',
        'username',
        'last_login_at',
        'failed_login_attempts',
        'locked_until',
        'pin',
      ])
      .executeTakeFirst()

    return result
  }

  async validateEmployee(
    username: string,
    password: string
  ): Promise<EmployeeResponse | null> {
    // Try to find by username first, then by email
    let employee = await this.getEmployeeByUsername(username)
    if (!employee) {
      employee = await this.getEmployeeByEmail(username)
    }

    if (!employee) {
      return null
    }

    // Check if account is locked
    if (employee.locked_until && new Date(employee.locked_until) > new Date()) {
      return null
    }

    let isValidCredential = false

    // Check if the provided password is a PIN (all numeric, 4-8 digits)
    if (this.isPinPassword(password)) {
      // Validate against stored PIN
      if (employee.pin && employee.pin === password) {
        isValidCredential = true
      }
    } else {
      // Validate against stored password hash
      if (employee.password_hash) {
        isValidCredential = await bcrypt.compare(
          password,
          employee.password_hash
        )
      }
    }

    if (!isValidCredential) {
      // Increment failed login attempts
      await this.incrementFailedLoginAttempts(employee.employee_id)
      return null
    }

    // Reset failed login attempts and update last login
    await this.resetFailedLoginAttempts(employee.employee_id)

    // Return employee without sensitive data
    const { password_hash, dui, nit, isss, pin, ...employeeResponse } = employee
    return { ...employeeResponse, pin: employee.pin } // Include PIN in response but not password_hash
  }

  async incrementFailedLoginAttempts(employee_id: number): Promise<void> {
    const employee = await db
      .selectFrom('employees')
      .select(['failed_login_attempts'])
      .where('employee_id', '=', employee_id)
      .executeTakeFirst()

    if (!employee) return

    const newAttempts = (employee.failed_login_attempts || 0) + 1
    const lockUntil =
      newAttempts >= 5 ? new Date(Date.now() + 30 * 60 * 1000) : null // Lock for 30 minutes after 5 attempts

    await db
      .updateTable('employees')
      .set({
        failed_login_attempts: newAttempts,
        locked_until: lockUntil?.toISOString(),
        updated_at: new Date().toISOString(),
      })
      .where('employee_id', '=', employee_id)
      .execute()
  }

  async resetFailedLoginAttempts(employee_id: number): Promise<void> {
    await db
      .updateTable('employees')
      .set({
        failed_login_attempts: 0,
        locked_until: null,
        last_login_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .where('employee_id', '=', employee_id)
      .execute()
  }

  async changePassword(
    employee_id: number,
    currentPassword: string,
    newPassword: string
  ): Promise<boolean> {
    const employee = await db
      .selectFrom('employees')
      .selectAll()
      .where('employee_id', '=', employee_id)
      .where('status', '=', 1)
      .executeTakeFirst()

    if (!employee) {
      return false
    }

    // Validate current credentials (either password or PIN)
    let isValidCurrentCredential = false

    if (this.isPinPassword(currentPassword)) {
      // Current credential is a PIN
      if (employee.pin && employee.pin === currentPassword) {
        isValidCurrentCredential = true
      }
    } else {
      // Current credential is a password
      if (employee.password_hash) {
        isValidCurrentCredential = await bcrypt.compare(
          currentPassword,
          employee.password_hash
        )
      }
    }

    if (!isValidCurrentCredential) {
      return false
    }

    return await this.updateEmployeePassword(employee_id, newPassword)
  }

  async setUsername(
    employee_id: number,
    username: string
  ): Promise<{ success: boolean; error?: string }> {
    // Validate username format
    const validation = this.validateUsername(username)
    if (!validation.isValid) {
      return { success: false, error: validation.error }
    }

    // Check if username already exists
    const existingEmployee = await this.getEmployeeByUsername(username)
    if (existingEmployee && existingEmployee.employee_id !== employee_id) {
      return { success: false, error: 'Username already exists' }
    }

    const result = await db
      .updateTable('employees')
      .set({
        username,
        updated_at: new Date().toISOString(),
      })
      .where('employee_id', '=', employee_id)
      .executeTakeFirst()

    return { success: result.numUpdatedRows > 0 }
  }

  /**
   * Generate a password reset code for an employee
   */
  async generatePasswordResetCode(
    employee_id: number
  ): Promise<{ success: boolean; code?: string; error?: string }> {
    try {
      // Check if employee exists
      const employee = await this.getEmployeeById(employee_id)
      if (!employee) {
        return { success: false, error: 'Employee not found' }
      }

      // Generate UUIDv4 code
      const resetCode = uuidv4()

      // Set expiry to 10 minutes from now
      const expiresAt = new Date()
      expiresAt.setMinutes(expiresAt.getMinutes() + 10)

      // Invalidate any existing unused codes for this employee
      await db
        .updateTable('password_reset_codes')
        .set({ used: true, used_at: new Date().toISOString() })
        .where('employee_id', '=', employee_id)
        .where('used', '=', false)
        .execute()

      // Insert new reset code
      await db
        .insertInto('password_reset_codes')
        .values({
          employee_id,
          reset_code: resetCode,
          expires_at: expiresAt.toISOString(),
          used: false,
        })
        .execute()

      return { success: true, code: resetCode }
    } catch (error) {
      console.error('Error generating password reset code:', error)
      return { success: false, error: 'Failed to generate reset code' }
    }
  }

  /**
   * Reset password using a reset code and employee_id
   */
  async resetPasswordWithCode(
    resetCode: string,
    employee_id: number,
    newPassword: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // Find valid reset code for the specific employee
      const resetRecord = await db
        .selectFrom('password_reset_codes')
        .selectAll()
        .where('reset_code', '=', resetCode)
        .where('employee_id', '=', employee_id)
        .where('used', '=', false)
        .where('expires_at', '>', new Date().toISOString())
        .executeTakeFirst()

      if (!resetRecord) {
        return {
          success: false,
          error: 'Invalid or expired reset code for this employee',
        }
      }

      // Update employee password
      const passwordUpdated = await this.updateEmployeePassword(
        resetRecord.employee_id,
        newPassword
      )
      if (!passwordUpdated) {
        return { success: false, error: 'Failed to update password' }
      }

      // Mark reset code as used
      await db
        .updateTable('password_reset_codes')
        .set({ used: true, used_at: new Date().toISOString() })
        .where('id', '=', resetRecord.id)
        .execute()

      return { success: true }
    } catch (error) {
      console.error('Error resetting password with code:', error)
      return { success: false, error: 'Failed to reset password' }
    }
  }

  /**
   * Clean up expired reset codes (should be called periodically)
   */
  async cleanupExpiredResetCodes(): Promise<number> {
    try {
      const result = await db
        .deleteFrom('password_reset_codes')
        .where('expires_at', '<', new Date().toISOString())
        .execute()

      return result.length
    } catch (error) {
      console.error('Error cleaning up expired reset codes:', error)
      return 0
    }
  }
}

export const employeeService = new EmployeeService()

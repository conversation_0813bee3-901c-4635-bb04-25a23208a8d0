import type { <PERSON>ys<PERSON> } from 'kysely'

// `any` is required here since migrations should be frozen in time. alternatively, keep a "snapshot" db interface.
export async function up(db: <PERSON><PERSON><PERSON><any>): Promise<void> {
  // modify username to be unique
  await db.schema
    .alterTable('employees')
    .addUniqueConstraint('username_unique', ['username'])
    .execute()

  // set unique constraint on email
  await db.schema
    .alterTable('employees')
    .addUniqueConstraint('email_unique', ['email'])
    .execute()

  // set employee_id to be unique and not null
  await db.schema
    .alterTable('employees')
    .addUniqueConstraint('employee_id_unique', ['employee_id'])
    .execute()
}

// `any` is required here since migrations should be frozen in time. alternatively, keep a "snapshot" db interface.
export async function down(db: <PERSON>ysely<any>): Promise<void> {
  // remove unique constraint on username
  await db.schema
    .alterTable('employees')
    .dropConstraint('username_unique')
    .execute()

  // remove unique constraint on email
  await db.schema
    .alterTable('employees')
    .dropConstraint('email_unique')
    .execute()

  // remove unique constraint on employee_id
  await db.schema
    .alterTable('employees')
    .dropConstraint('employee_id_unique')
    .execute()
}

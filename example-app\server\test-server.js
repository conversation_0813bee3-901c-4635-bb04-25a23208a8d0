import axios from 'axios'

const SERVER_URL = 'http://localhost:3001'
const OAUTH_SERVER_URL = 'http://localhost:3230'

// Test credentials
const TEST_CREDENTIALS = {
  username: 'william de jesus.rivera marroquin',
  password: 'admin123',
  client_id: '07b2f434-ec60-4cac-aa53-f17008ab5e6d',
  client_secret:
    '6eac4814cddc514c84cffaf691aae3b3d7dd83b12c56a3ea93f3aa226c0576fa',
}

async function testServer() {
  try {
    console.log('🧪 Testing App Example Server...\n')

    // Test 1: Server health check
    console.log('1. Testing server health...')
    const healthResponse = await axios.get(`${SERVER_URL}/api/health`)
    console.log('✅ Health check passed')
    console.log(`   Status: ${healthResponse.data.status}`)
    console.log(`   Server: ${healthResponse.data.server}`)

    // Test 2: Server info (no auth)
    console.log('\n2. Testing server info endpoint...')
    const infoResponse = await axios.get(`${SERVER_URL}/api/info`)
    console.log('✅ Server info retrieved')
    console.log(`   Authenticated: ${infoResponse.data.authenticated}`)
    console.log(`   Features: ${infoResponse.data.features.length}`)

    // Test 3: Get OAuth token
    console.log('\n3. Getting OAuth token...')
    const tokenResponse = await axios.post(`${OAUTH_SERVER_URL}/oauth/token`, {
      grant_type: 'password',
      username: TEST_CREDENTIALS.username,
      password: TEST_CREDENTIALS.password,
      client_id: TEST_CREDENTIALS.client_id,
      client_secret: TEST_CREDENTIALS.client_secret,
      scope: 'read write',
    })

    const accessToken = tokenResponse.data.access_token
    console.log('✅ OAuth token obtained')
    console.log(`   Token type: ${tokenResponse.data.token_type}`)
    console.log(`   Expires in: ${tokenResponse.data.expires_in} seconds`)

    // Test 4: Server info with auth
    console.log('\n4. Testing server info with authentication...')
    const authInfoResponse = await axios.get(`${SERVER_URL}/api/info`, {
      headers: { Authorization: `Bearer ${accessToken}` },
    })
    console.log('✅ Authenticated server info retrieved')
    console.log(`   Authenticated: ${authInfoResponse.data.authenticated}`)
    console.log(`   Employee ID: ${authInfoResponse.data.user?.employee_id}`)
    console.log(
      `   Employee Name: ${authInfoResponse.data.employee?.full_name}`
    )
    console.log(`   Data Source: ${authInfoResponse.data.employee?.source}`)

    // Test 5: Token verification endpoint
    console.log('\n5. Testing token verification...')
    const verifyResponse = await axios.get(`${SERVER_URL}/api/verify-token`, {
      headers: { Authorization: `Bearer ${accessToken}` },
    })
    console.log('✅ Token verification successful')
    console.log(`   Token valid: ${verifyResponse.data.token_valid}`)
    console.log(`   Employee ID: ${verifyResponse.data.payload.employee_id}`)
    console.log(
      `   Verification method: ${verifyResponse.data.verification_method}`
    )

    // Test 6: Employee profile endpoint
    console.log('\n6. Testing employee profile endpoint...')
    const profileResponse = await axios.get(`${SERVER_URL}/api/profile`, {
      headers: { Authorization: `Bearer ${accessToken}` },
    })
    console.log('✅ Employee profile retrieved')
    console.log(
      `   Employee: ${profileResponse.data.employee.first_name} ${profileResponse.data.employee.last_name}`
    )
    console.log(`   Full Name: ${profileResponse.data.employee.full_name}`)
    console.log(
      `   Display Name: ${profileResponse.data.employee.display_name}`
    )
    console.log(
      `   Data Source: ${profileResponse.data.employee.data_source || 'unknown'}`
    )

    // Test 7: Dashboard endpoint
    console.log('\n7. Testing dashboard endpoint...')
    const dashboardResponse = await axios.get(`${SERVER_URL}/api/dashboard`, {
      headers: { Authorization: `Bearer ${accessToken}` },
    })
    console.log('✅ Dashboard data retrieved')
    console.log(
      `   Welcome: ${dashboardResponse.data.dashboard.welcome_message}`
    )
    console.log(
      `   Permissions: ${dashboardResponse.data.dashboard.permissions.scope}`
    )
    console.log(
      `   Token expires in: ${dashboardResponse.data.dashboard.server_info.token_expires_in} seconds`
    )

    // Test 8: Tasks endpoint
    console.log('\n8. Testing tasks endpoint...')
    const tasksResponse = await axios.get(`${SERVER_URL}/api/tasks`, {
      headers: { Authorization: `Bearer ${accessToken}` },
    })
    console.log('✅ Tasks retrieved')
    console.log(`   Total tasks: ${tasksResponse.data.summary.total}`)
    console.log(`   Completed: ${tasksResponse.data.summary.completed}`)
    console.log(`   In progress: ${tasksResponse.data.summary.in_progress}`)

    // Test 9: Middleware test endpoint
    console.log('\n9. Testing middleware chain...')
    const middlewareResponse = await axios.get(`${SERVER_URL}/test-auth`, {
      headers: { Authorization: `Bearer ${accessToken}` },
    })
    console.log('✅ Middleware chain test successful')
    console.log(
      `   Employee: ${middlewareResponse.data.employee_info.first_name} ${middlewareResponse.data.employee_info.last_name}`
    )
    console.log(
      `   Middleware chain: ${middlewareResponse.data.middleware_chain.length} steps`
    )

    // Test 10: Error handling (invalid token)
    console.log('\n10. Testing error handling with invalid token...')
    try {
      await axios.get(`${SERVER_URL}/api/profile`, {
        headers: { Authorization: 'Bearer invalid-token' },
      })
    } catch (error) {
      if (error.response?.status === 401) {
        console.log('✅ Invalid token properly rejected')
        console.log(`   Error: ${error.response.data.error}`)
        console.log(`   Message: ${error.response.data.message}`)
      } else {
        throw error
      }
    }

    console.log('\n🎉 All server tests passed successfully!')
    console.log('\n📋 Server Test Summary:')
    console.log('   ✅ Health check endpoint working')
    console.log('   ✅ Optional authentication working')
    console.log('   ✅ JWT token verification working')
    console.log('   ✅ Employee middleware working')
    console.log('   ✅ Protected endpoints accessible')
    console.log('   ✅ Error handling working')
    console.log('   ✅ JWKS integration working')
    console.log('   ✅ OAuth server communication working')
  } catch (error) {
    console.error('❌ Server test failed:', error.message)
    if (error.response) {
      console.error('Response status:', error.response.status)
      console.error('Response data:', error.response.data)
    }
    process.exit(1)
  }
}

testServer()

import {
  ColumnType,
  Generated,
  Insertable,
  Selectable,
  Updateable,
} from 'kysely'

export interface OAuthClientsTable {
  id: Generated<number>
  client_id: string
  client_secret: string
  client_name: string
  redirect_uris: string // JSON string in MySQL
  grant_types: string // JSON string in MySQL
  scope: string // space-separated scopes
  is_active: boolean
  webhook_url: string | null // URL to send webhook notifications
  webhook_secret: string | null // Secret for webhook signature verification
  webhook_events: string | null // JSON array of events to subscribe to
  created_at: ColumnType<Date, string | undefined, never>
  updated_at: ColumnType<Date, string | undefined, string | undefined>
}

// Export types for OAuth Clients
export type OAuthClient = Selectable<OAuthClientsTable>
export type NewOAuthClient = Insertable<OAuthClientsTable>
export type OAuthClientUpdate = Updateable<OAuthClientsTable>

<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Reset Password - Var<PERSON>ro Auth</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family:
          -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu,
          Cantarell, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px;
      }

      .container {
        background: white;
        border-radius: 12px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        padding: 40px;
        width: 100%;
        max-width: 400px;
      }

      .header {
        text-align: center;
        margin-bottom: 30px;
      }

      .header h1 {
        color: #333;
        font-size: 28px;
        font-weight: 600;
        margin-bottom: 8px;
      }

      .header p {
        color: #666;
        font-size: 14px;
      }

      .form-group {
        margin-bottom: 20px;
      }

      .form-group label {
        display: block;
        margin-bottom: 8px;
        color: #333;
        font-weight: 500;
        font-size: 14px;
      }

      .form-group input {
        width: 100%;
        padding: 12px 16px;
        border: 2px solid #e1e5e9;
        border-radius: 8px;
        font-size: 16px;
        transition: border-color 0.3s ease;
      }

      .form-group input:focus {
        outline: none;
        border-color: #667eea;
      }

      .form-group input.error {
        border-color: #e74c3c;
      }

      .error-message {
        color: #e74c3c;
        font-size: 12px;
        margin-top: 5px;
        display: none;
      }

      .password-requirements {
        background: #f8f9fa;
        border-radius: 6px;
        padding: 12px;
        margin-top: 10px;
        font-size: 12px;
        color: #666;
      }

      .password-requirements ul {
        margin: 8px 0 0 16px;
      }

      .password-requirements li {
        margin-bottom: 4px;
      }

      .btn {
        width: 100%;
        padding: 14px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 8px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition:
          transform 0.2s ease,
          box-shadow 0.2s ease;
      }

      .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
      }

      .btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
      }

      .alert {
        padding: 12px 16px;
        border-radius: 6px;
        margin-bottom: 20px;
        font-size: 14px;
        display: none;
      }

      .alert.error {
        background: #fdf2f2;
        color: #e74c3c;
        border: 1px solid #fecaca;
      }

      .alert.success {
        background: #f0fdf4;
        color: #16a34a;
        border: 1px solid #bbf7d0;
      }

      .alert.info {
        background: #eff6ff;
        color: #2563eb;
        border: 1px solid #bfdbfe;
      }

      .back-link {
        text-align: center;
        margin-top: 20px;
      }

      .back-link a {
        color: #667eea;
        text-decoration: none;
        font-size: 14px;
      }

      .back-link a:hover {
        text-decoration: underline;
      }

      .code-info {
        background: #eff6ff;
        border: 1px solid #bfdbfe;
        border-radius: 6px;
        padding: 12px;
        margin-bottom: 20px;
        font-size: 13px;
        color: #1e40af;
      }

      .code-display {
        font-family: 'Courier New', monospace;
        font-weight: bold;
        font-size: 16px;
        letter-spacing: 2px;
        text-align: center;
        padding: 8px;
        background: #f8fafc;
        border-radius: 4px;
        margin: 8px 0;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>Reset Password</h1>
        <p>Enter your reset code and new password</p>
      </div>

      <div id="alert" class="alert"></div>

      <div class="code-info" id="codeInfo" style="display: none">
        <strong>Reset Information:</strong>
        <div>
          <strong>Employee ID:</strong> <span id="employeeIdDisplay"></span>
        </div>
        <div>
          <strong>Reset Code:</strong>
          <span
            id="codeDisplay"
            style="
              font-family: monospace;
              font-size: 12px;
              word-break: break-all;
            "
          ></span>
        </div>
        <small
          >This information was provided to you by your administrator or via the
          reset link.</small
        >
      </div>

      <form id="resetPasswordForm">
        <div class="form-group">
          <label for="employeeId">Employee ID</label>
          <input
            type="number"
            id="employeeId"
            name="employeeId"
            placeholder="Enter your employee ID"
            required
          />
          <div class="error-message" id="employeeIdError"></div>
        </div>

        <div class="form-group">
          <label for="resetCode">Reset Code</label>
          <input
            type="text"
            id="resetCode"
            name="resetCode"
            placeholder="Enter your reset code"
            required
          />
          <div class="error-message" id="resetCodeError"></div>
        </div>

        <div class="form-group">
          <label for="newPassword">New Password</label>
          <input type="password" id="newPassword" name="newPassword" required />
          <div class="error-message" id="newPasswordError"></div>
          <div class="password-requirements">
            <strong>Password/PIN Requirements:</strong>
            <ul>
              <li>
                <strong>Password:</strong> At least 6 characters with at least 1
                letter
              </li>
              <li><strong>PIN:</strong> 4-8 numeric digits only</li>
              <li>Mix of letters and numbers recommended for passwords</li>
              <li>Avoid common passwords</li>
            </ul>
          </div>
        </div>

        <div class="form-group">
          <label for="confirmPassword">Confirm New Password</label>
          <input
            type="password"
            id="confirmPassword"
            name="confirmPassword"
            required
          />
          <div class="error-message" id="confirmPasswordError"></div>
        </div>

        <button type="submit" class="btn" id="submitBtn">Reset Password</button>
      </form>

      <div class="back-link">
        <a href="/">← Back to Login</a>
      </div>
    </div>

    <script>
      const form = document.getElementById('resetPasswordForm')
      const alert = document.getElementById('alert')
      const submitBtn = document.getElementById('submitBtn')
      const codeInfo = document.getElementById('codeInfo')
      const codeDisplay = document.getElementById('codeDisplay')

      // Check if reset code and employee_id are provided in URL
      const urlParams = new URLSearchParams(window.location.search)
      const resetCodeFromUrl = urlParams.get('code')
      const employeeIdFromUrl = urlParams.get('employee_id')

      if (resetCodeFromUrl && employeeIdFromUrl) {
        document.getElementById('resetCode').value = resetCodeFromUrl
        document.getElementById('employeeId').value = employeeIdFromUrl
        codeDisplay.textContent = resetCodeFromUrl
        document.getElementById('employeeIdDisplay').textContent =
          employeeIdFromUrl
        codeInfo.style.display = 'block'
      }

      // Form validation
      function validateForm() {
        let isValid = true
        clearErrors()

        const employeeId = document.getElementById('employeeId').value
        const resetCode = document.getElementById('resetCode').value
        const newPassword = document.getElementById('newPassword').value
        const confirmPassword = document.getElementById('confirmPassword').value

        // Employee ID validation
        if (!employeeId || employeeId <= 0) {
          showFieldError('employeeId', 'Please enter a valid employee ID')
          isValid = false
        }

        // Reset code validation
        if (!resetCode) {
          showFieldError('resetCode', 'Reset code is required')
          isValid = false
        } else if (
          !/^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(
            resetCode
          )
        ) {
          showFieldError('resetCode', 'Invalid reset code format')
          isValid = false
        }

        // New password validation
        if (!newPassword) {
          showFieldError('newPassword', 'New password is required')
          isValid = false
        } else {
          // Check if it's a PIN (4-8 digits only)
          const isPin = /^\d{4,8}$/.test(newPassword)

          if (isPin) {
            // PIN validation
            if (newPassword.length < 4 || newPassword.length > 8) {
              showFieldError('newPassword', 'PIN must be 4-8 digits long')
              isValid = false
            }
          } else {
            // Password validation
            if (newPassword.length < 6) {
              showFieldError(
                'newPassword',
                'Password must be at least 6 characters long'
              )
              isValid = false
            } else if (!/[a-zA-Z]/.test(newPassword)) {
              showFieldError(
                'newPassword',
                'Password must contain at least 1 alphabetic character'
              )
              isValid = false
            }
          }
        }

        // Confirm password validation
        if (!confirmPassword) {
          showFieldError('confirmPassword', 'Please confirm your new password')
          isValid = false
        } else if (newPassword !== confirmPassword) {
          showFieldError('confirmPassword', 'Passwords do not match')
          isValid = false
        }

        return isValid
      }

      function showFieldError(fieldId, message) {
        const field = document.getElementById(fieldId)
        const errorElement = document.getElementById(fieldId + 'Error')
        field.classList.add('error')
        errorElement.textContent = message
        errorElement.style.display = 'block'
      }

      function clearErrors() {
        const fields = [
          'employeeId',
          'resetCode',
          'newPassword',
          'confirmPassword',
        ]
        fields.forEach((fieldId) => {
          const field = document.getElementById(fieldId)
          const errorElement = document.getElementById(fieldId + 'Error')
          field.classList.remove('error')
          errorElement.style.display = 'none'
        })
        hideAlert()
      }

      function showAlert(message, type) {
        alert.textContent = message
        alert.className = `alert ${type}`
        alert.style.display = 'block'
      }

      function hideAlert() {
        alert.style.display = 'none'
      }

      // Form submission
      form.addEventListener('submit', async (e) => {
        e.preventDefault()

        if (!validateForm()) {
          return
        }

        const formData = new FormData(form)
        const data = {
          reset_code: formData.get('resetCode'),
          employee_id: parseInt(formData.get('employeeId')),
          new_password: formData.get('newPassword'),
        }

        submitBtn.disabled = true
        submitBtn.textContent = 'Resetting Password...'

        try {
          const response = await fetch('/api/v1/employees/reset-password', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(data),
          })

          const result = await response.json()

          if (response.ok) {
            showAlert(
              'Password reset successfully! You can now log in with your new password.',
              'success'
            )
            form.reset()
            setTimeout(() => {
              window.location.href = '/'
            }, 3000)
          } else {
            showAlert(result.message || 'Failed to reset password', 'error')
          }
        } catch (error) {
          showAlert('Network error. Please try again.', 'error')
        } finally {
          submitBtn.disabled = false
          submitBtn.textContent = 'Reset Password'
        }
      })

      // Real-time password confirmation validation
      document
        .getElementById('confirmPassword')
        .addEventListener('input', function () {
          const newPassword = document.getElementById('newPassword').value
          const confirmPassword = this.value

          if (confirmPassword && newPassword !== confirmPassword) {
            showFieldError('confirmPassword', 'Passwords do not match')
          } else {
            const errorElement = document.getElementById('confirmPasswordError')
            errorElement.style.display = 'none'
            this.classList.remove('error')
          }
        })

      // Format reset code input (numbers only)
      document
        .getElementById('resetCode')
        .addEventListener('input', function () {
          this.value = this.value.replace(/\D/g, '').substring(0, 6)
        })
    </script>
  </body>
</html>

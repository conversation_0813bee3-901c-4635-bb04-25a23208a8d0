import React from 'react'
import { Link, useLocation } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext.jsx'

const Navbar = () => {
  const { user, isAuthenticated, logout } = useAuth()
  const location = useLocation()

  const isActive = (path) => location.pathname === path

  const handleLogout = async () => {
    await logout()
  }

  return (
    <nav className="nav">
      <div className="nav-content">
        <Link to="/" className="nav-brand">
          OAuth Client Example
        </Link>
        
        <div className="nav-links">
          {isAuthenticated ? (
            <>
              <Link 
                to="/dashboard" 
                className={`nav-link ${isActive('/dashboard') ? 'active' : ''}`}
              >
                Dashboard
              </Link>
              <Link 
                to="/profile" 
                className={`nav-link ${isActive('/profile') ? 'active' : ''}`}
              >
                Profile
              </Link>
              <Link 
                to="/tokens" 
                className={`nav-link ${isActive('/tokens') ? 'active' : ''}`}
              >
                Tokens
              </Link>
              <span className="nav-link text-muted">
                Welcome, {user?.first_name || 'User'}
              </span>
              <button onClick={handleLogout} className="btn btn-secondary">
                Logout
              </button>
            </>
          ) : (
            <Link to="/login" className="btn">
              Login
            </Link>
          )}
        </div>
      </div>
    </nav>
  )
}

export default Navbar

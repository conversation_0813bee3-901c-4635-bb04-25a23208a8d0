# OAuth 2.0 React Client

A React frontend application demonstrating OAuth 2.0 authentication with RS256 JWT tokens.

## 🎯 Purpose

This React client demonstrates how to integrate with an OAuth 2.0 authorization server, showcasing both Password Grant and Authorization Code Grant flows with proper token verification and management.

## ✨ Features

### 🔐 OAuth 2.0 Flows
- **Password Grant** - Direct username/password authentication
- **Authorization Code Grant** - Redirect-based authentication
- **Token Refresh** - Automatic token renewal
- **PKCE Support** - Enhanced security for public clients

### 🛡️ Security Features
- **RS256 Verification** - JWT signature validation using public keys
- **JWKS Integration** - Automatic public key discovery and caching
- **Token Storage** - Secure token management in localStorage
- **Route Protection** - Authentication-based route guards

### 📱 User Experience
- **Responsive Design** - Works on desktop and mobile
- **Real-time Updates** - Live token status and verification
- **Error Handling** - Comprehensive error messages and recovery
- **Loading States** - User-friendly loading indicators

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ installed
- OAuth server running on `http://localhost:3230`
- Backend server running on `http://localhost:3001`

### Installation
```bash
cd app-example/client
npm install
```

### Configuration
Edit `src/config/oauth.js` with your OAuth server details:
```javascript
export const OAUTH_CONFIG = {
  authServerUrl: 'http://localhost:3230',
  clientId: 'your-client-id',
  clientSecret: 'your-client-secret',
  redirectUri: 'http://localhost:3000/callback',
  scope: 'read write'
}
```

### Start Development Server
```bash
npm run dev
```

Application runs on: `http://localhost:3000`

## 📖 Usage Guide

### 1. Login Methods

#### Password Flow
- Enter username and password directly
- Immediate authentication without redirects
- Best for trusted applications

#### Authorization Code Flow
- Redirects to OAuth server login page
- More secure for public clients
- Supports PKCE for enhanced security

### 2. Dashboard Features

- **User Information** - Display authenticated user details
- **Token Analysis** - Real-time JWT token inspection
- **Server Metadata** - OAuth server configuration
- **Verification Status** - RS256 signature validation

### 3. Token Management

- **Automatic Refresh** - Tokens renewed before expiry
- **Manual Refresh** - Force token renewal
- **Token Inspection** - Decode and verify JWT claims
- **JWKS Integration** - Public key discovery and caching

## 🏗️ Architecture

### Component Structure
```
src/
├── components/          # Reusable UI components
│   ├── Navbar.jsx      # Navigation with auth state
│   └── LoadingSpinner.jsx
├── contexts/           # React contexts
│   └── AuthContext.jsx # Authentication state management
├── pages/              # Route components
│   ├── LoginPage.jsx   # Authentication forms
│   ├── DashboardPage.jsx # Main dashboard
│   ├── TokenPage.jsx   # Token analysis
│   ├── ProfilePage.jsx # User profile
│   └── CallbackPage.jsx # OAuth callback handler
├── services/           # Business logic
│   └── oauthService.js # OAuth integration
└── config/             # Configuration
    └── oauth.js        # OAuth settings
```

### Key Services

#### OAuthService
- Token management and storage
- API communication with OAuth server
- JWT verification using JWKS
- Automatic token refresh logic

#### AuthContext
- Global authentication state
- User session management
- Error handling and loading states
- Route protection logic

## 🛠️ Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

### Testing OAuth Flows

1. **Start OAuth Server** - Ensure server is running on port 3230
2. **Start Backend Server** - Ensure server is running on port 3001
3. **Seed Test Data** - Run server seed script for test users
4. **Test Credentials:**
   - Username: `william de jesus.rivera marroquin`
   - Password: `admin123`

### Debugging

- Check browser console for detailed logs
- Inspect Network tab for API requests
- Use Token page for JWT analysis
- Verify JWKS endpoint accessibility

## 🔒 Security Considerations

### Token Storage
- Tokens stored in localStorage (consider httpOnly cookies for production)
- Automatic cleanup on logout
- Expiry-based validation

### CSRF Protection
- State parameter validation in authorization code flow
- Secure redirect URI validation

### Token Verification
- Always verify JWT signatures using public key
- Validate all standard JWT claims (iss, aud, exp)
- Handle verification errors gracefully

## 📚 Integration with Backend

This client is designed to work with the companion Express server (`../server/`) which provides:

- **HTTP-only Cookie Support** - Secure token storage
- **Employee Information API** - User profile endpoints
- **Protected Resources** - Authenticated API endpoints
- **Token Verification** - Server-side JWT validation

### API Endpoints Used

- `GET /api/auth/me` - Get current user information
- `POST /api/auth/login` - Login with HTTP-only cookies
- `POST /api/auth/logout` - Logout and clear cookies
- `GET /api/profile` - Get user profile
- `GET /api/dashboard` - Get dashboard data

## 🤝 Contributing

This is an example application for demonstration purposes. Feel free to:
- Fork and modify for your use case
- Report issues or suggest improvements
- Use as a reference for your OAuth integration

---

**Happy OAuth Integration!** 🎉

This React client demonstrates production-ready OAuth 2.0 integration patterns with modern security practices.

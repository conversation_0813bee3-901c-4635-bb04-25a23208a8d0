import { Router } from 'express'
import * as clientController from '../../controllers/clientController'

export const clientRouter = Router()

// GET /api/v1/clients - Get all OAuth clients
clientRouter.get('/', clientController.getAllClients)

// GET /api/v1/clients/:id - Get client by ID
clientRouter.get('/:id', clientController.getClientById)

// POST /api/v1/clients - Create new OAuth client
clientRouter.post('/', clientController.createClient)

// PUT /api/v1/clients/:id - Update OAuth client
clientRouter.put('/:id', clientController.updateClient)

// DELETE /api/v1/clients/:id - Delete OAuth client (soft delete)
clientRouter.delete('/:id', clientController.deleteClient)

// POST /api/v1/clients/exchange-token - Exchange authorization code for tokens
clientRouter.post('/exchange-token', clientController.exchangeToken)

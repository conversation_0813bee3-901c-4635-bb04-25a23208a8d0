import React, { useState } from 'react'
import { useAuth } from '../contexts/AuthContext.jsx'
import axios from 'axios'
import { getOAuthUrl } from '../config/oauth.js'

const ProfilePage = () => {
  const { user, refreshUserInfo } = useAuth()
  const [loading, setLoading] = useState(false)
  const [apiTestResult, setApiTestResult] = useState(null)

  const handleRefreshProfile = async () => {
    try {
      setLoading(true)
      await refreshUserInfo()
      alert('Profile refreshed successfully!')
    } catch (error) {
      console.error('Failed to refresh profile:', error)
      alert('Failed to refresh profile')
    } finally {
      setLoading(false)
    }
  }

  const testProtectedAPI = async () => {
    try {
      setLoading(true)
      setApiTestResult(null)

      // Test multiple API endpoints
      const tests = [
        {
          name: 'Get User Profile',
          endpoint: '/api/v1/users/me',
          method: 'GET'
        },
        {
          name: 'Get All Users',
          endpoint: '/api/v1/users',
          method: 'GET'
        }
      ]

      const results = []

      for (const test of tests) {
        try {
          const response = await axios({
            method: test.method,
            url: getOAuthUrl(test.endpoint)
          })

          results.push({
            ...test,
            success: true,
            status: response.status,
            data: response.data
          })
        } catch (error) {
          results.push({
            ...test,
            success: false,
            status: error.response?.status || 'Network Error',
            error: error.response?.data || error.message
          })
        }
      }

      setApiTestResult(results)
    } catch (error) {
      console.error('API test failed:', error)
      setApiTestResult([{
        name: 'API Test',
        success: false,
        error: error.message
      }])
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container">
      <div className="card">
        <h1>👤 User Profile</h1>
        <p className="text-muted">
          Your profile information retrieved from the OAuth-protected API endpoint.
        </p>
      </div>

      <div className="grid grid-2">
        {/* Profile Information */}
        <div className="card">
          <h2>Profile Information</h2>
          {user ? (
            <div>
              <div className="form-group">
                <label className="form-label">Full Name</label>
                <div className="form-input" style={{ backgroundColor: '#f8f9fa' }}>
                  {user.first_name} {user.last_name}
                </div>
              </div>

              <div className="form-group">
                <label className="form-label">Employee ID</label>
                <div className="form-input" style={{ backgroundColor: '#f8f9fa' }}>
                  {user.employee_id}
                </div>
              </div>

              <div className="form-group">
                <label className="form-label">Email</label>
                <div className="form-input" style={{ backgroundColor: '#f8f9fa' }}>
                  {user.email || 'Not provided'}
                </div>
              </div>

              <div className="form-group">
                <label className="form-label">Department</label>
                <div className="form-input" style={{ backgroundColor: '#f8f9fa' }}>
                  {user.department || 'Not specified'}
                </div>
              </div>

              <div className="form-group">
                <label className="form-label">Position</label>
                <div className="form-input" style={{ backgroundColor: '#f8f9fa' }}>
                  {user.position || 'Not specified'}
                </div>
              </div>

              <div className="form-group">
                <label className="form-label">Status</label>
                <div className="form-input" style={{ 
                  backgroundColor: user.is_active ? '#d4edda' : '#f8d7da',
                  color: user.is_active ? '#155724' : '#721c24'
                }}>
                  {user.is_active ? '✅ Active' : '❌ Inactive'}
                </div>
              </div>

              <button 
                onClick={handleRefreshProfile}
                className="btn"
                disabled={loading}
              >
                {loading ? 'Refreshing...' : 'Refresh Profile'}
              </button>
            </div>
          ) : (
            <p>No profile information available</p>
          )}
        </div>

        {/* API Testing */}
        <div className="card">
          <h2>Protected API Testing</h2>
          <p className="text-muted">
            Test OAuth-protected API endpoints using your current access token.
          </p>

          <button 
            onClick={testProtectedAPI}
            className="btn"
            disabled={loading}
          >
            {loading ? 'Testing APIs...' : 'Test Protected APIs'}
          </button>

          {apiTestResult && (
            <div className="mt-3">
              <h3>API Test Results</h3>
              {apiTestResult.map((result, index) => (
                <div key={index} className="mt-2">
                  <div className={`alert ${result.success ? 'alert-success' : 'alert-error'}`}>
                    <strong>{result.name}</strong><br />
                    <strong>Status:</strong> {result.status}<br />
                    <strong>Method:</strong> {result.method} {result.endpoint}
                  </div>

                  {result.success && result.data && (
                    <div className="code-block">
                      {JSON.stringify(result.data, null, 2)}
                    </div>
                  )}

                  {!result.success && result.error && (
                    <div className="code-block">
                      {JSON.stringify(result.error, null, 2)}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Raw Profile Data */}
      <div className="card">
        <h2>Raw Profile Data</h2>
        <p className="text-muted">
          Complete user object as returned by the OAuth server.
        </p>
        {user ? (
          <div className="code-block">
            {JSON.stringify(user, null, 2)}
          </div>
        ) : (
          <p>No profile data available</p>
        )}
      </div>

      {/* OAuth Integration Info */}
      <div className="card">
        <h2>🔧 OAuth Integration Details</h2>
        <div className="grid grid-2">
          <div>
            <h3>Authentication Flow</h3>
            <ul>
              <li>✅ User authenticated via OAuth 2.0</li>
              <li>✅ Access token obtained with RS256 signature</li>
              <li>✅ Token automatically included in API requests</li>
              <li>✅ Profile data fetched from protected endpoint</li>
              <li>✅ Token refresh handled automatically</li>
            </ul>
          </div>
          
          <div>
            <h3>Security Features</h3>
            <ul>
              <li>🔐 RS256 asymmetric token verification</li>
              <li>🔐 Public key distributed via JWKS</li>
              <li>🔐 Automatic token expiry handling</li>
              <li>🔐 Secure token storage in browser</li>
              <li>🔐 CSRF protection with state parameter</li>
            </ul>
          </div>
        </div>

        <div className="alert alert-info mt-3">
          <strong>How it works:</strong><br />
          This profile page demonstrates how client applications can securely access protected resources
          using OAuth 2.0 access tokens. The token is automatically included in API requests via axios
          interceptors, and the server verifies the token using the RS256 public key before returning
          the protected user data.
        </div>
      </div>
    </div>
  )
}

export default ProfilePage

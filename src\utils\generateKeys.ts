import * as crypto from 'crypto'
import * as fs from 'fs'
import * as path from 'path'

export function generateRSAKeyPair(): {
  privateKey: string
  publicKey: string
} {
  const { privateKey, publicKey } = crypto.generateKeyPairSync('rsa', {
    modulusLength: 2048,
    publicKeyEncoding: {
      type: 'spki',
      format: 'pem',
    },
    privateKeyEncoding: {
      type: 'pkcs8',
      format: 'pem',
    },
  })

  return { privateKey, publicKey }
}

export function saveKeysToFiles(
  privateKey: string,
  publicKey: string,
  keysDir: string = 'keys'
): void {
  // Create keys directory if it doesn't exist
  if (!fs.existsSync(keysDir)) {
    fs.mkdirSync(keysDir, { recursive: true })
  }

  // Save private key
  const privateKeyPath = path.join(keysDir, 'private.pem')
  fs.writeFileSync(privateKeyPath, privateKey)
  console.log(`Private key saved to: ${privateKeyPath}`)

  // Save public key
  const publicKeyPath = path.join(keysDir, 'public.pem')
  fs.writeFileSync(publicKeyPath, publicKey)
  console.log(`Public key saved to: ${publicKeyPath}`)

  // Update .env file with key paths
  const envPath = '.env'
  let envContent = ''

  if (fs.existsSync(envPath)) {
    envContent = fs.readFileSync(envPath, 'utf8')
  }

  // Remove existing JWT key configurations
  envContent = envContent.replace(/^JWT_ALGORITHM=.*$/m, '')
  envContent = envContent.replace(/^JWT_SECRET=.*$/m, '')
  envContent = envContent.replace(/^JWT_PRIVATE_KEY_PATH=.*$/m, '')
  envContent = envContent.replace(/^JWT_PUBLIC_KEY_PATH=.*$/m, '')

  // Add new RS256 configuration
  envContent += `\n# JWT RS256 Configuration\n`
  envContent += `JWT_ALGORITHM=RS256\n`
  envContent += `JWT_PRIVATE_KEY_PATH=${privateKeyPath}\n`
  envContent += `JWT_PUBLIC_KEY_PATH=${publicKeyPath}\n`

  // Clean up extra newlines
  envContent = envContent.replace(/\n\n+/g, '\n\n').trim() + '\n'

  fs.writeFileSync(envPath, envContent)
  console.log(`Updated .env file with RS256 configuration`)
}

// CLI usage
if (require.main === module) {
  console.log('🔐 Generating RSA key pair for JWT RS256...\n')

  const { privateKey, publicKey } = generateRSAKeyPair()
  saveKeysToFiles(privateKey, publicKey)

  console.log('\n✅ RSA key pair generated successfully!')
  console.log('\n📋 Next steps:')
  console.log('1. Restart your OAuth server to use the new RS256 algorithm')
  console.log(
    '2. Update any clients that need to verify JWT tokens with the public key'
  )
  console.log('3. The private key is used for signing (keep secure)')
  console.log('4. The public key can be shared for token verification')
}

import React, { useState, useEffect } from 'react'
import { useAuth } from '../contexts/AuthContext.jsx'
import oauthService from '../services/oauthService.js'
import { OAUTH_CONFIG } from '../config/oauth.js'

const DashboardPage = () => {
  const { user } = useAuth()
  const [serverMetadata, setServerMetadata] = useState(null)
  const [tokenInfo, setTokenInfo] = useState(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    try {
      setLoading(true)

      // Get server metadata
      const metadata = await oauthService.getServerMetadata()
      setServerMetadata(metadata)

      // Get current token info
      const accessToken = oauthService.getAccessToken()
      if (accessToken) {
        const decoded = oauthService.decodeToken(accessToken)
        const verification = await oauthService.verifyToken(accessToken)
        
        setTokenInfo({
          decoded,
          verification,
          raw: accessToken
        })
      }
    } catch (error) {
      console.error('Failed to load dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatDate = (timestamp) => {
    return new Date(timestamp * 1000).toLocaleString()
  }

  if (loading) {
    return (
      <div className="loading">
        <div className="spinner"></div>
        <p className="mt-2">Loading dashboard...</p>
      </div>
    )
  }

  return (
    <div className="container">
      <div className="card">
        <h1>🎉 Welcome to the OAuth Client Dashboard!</h1>
        <p className="text-muted">
          You have successfully authenticated using OAuth 2.0 with RS256 JWT tokens.
        </p>
      </div>

      <div className="grid grid-2">
        {/* User Information */}
        <div className="card">
          <h2>👤 User Information</h2>
          {user ? (
            <div>
              <p><strong>Name:</strong> {user.first_name} {user.last_name}</p>
              <p><strong>Employee ID:</strong> {user.employee_id}</p>
              <p><strong>Email:</strong> {user.email || 'Not provided'}</p>
              <p><strong>Department:</strong> {user.department || 'Not specified'}</p>
              <p><strong>Position:</strong> {user.position || 'Not specified'}</p>
              <p><strong>Status:</strong> 
                <span style={{ 
                  color: user.is_active ? 'green' : 'red',
                  fontWeight: 'bold'
                }}>
                  {user.is_active ? 'Active' : 'Inactive'}
                </span>
              </p>
            </div>
          ) : (
            <p>No user information available</p>
          )}
        </div>

        {/* OAuth Server Info */}
        <div className="card">
          <h2>🔧 OAuth Server Information</h2>
          {serverMetadata ? (
            <div>
              <p><strong>Issuer:</strong> {serverMetadata.issuer}</p>
              <p><strong>Algorithm:</strong> {serverMetadata.id_token_signing_alg_values_supported?.[0]}</p>
              <p><strong>Grant Types:</strong> {serverMetadata.grant_types_supported?.join(', ')}</p>
              <p><strong>Scopes:</strong> {serverMetadata.scopes_supported?.join(', ')}</p>
              <p><strong>JWKS URI:</strong> 
                <a href={serverMetadata.jwks_uri} target="_blank" rel="noopener noreferrer">
                  {serverMetadata.jwks_uri}
                </a>
              </p>
            </div>
          ) : (
            <p>Failed to load server metadata</p>
          )}
        </div>
      </div>

      {/* Token Information */}
      <div className="card">
        <h2>🔐 Current Token Information</h2>
        {tokenInfo ? (
          <div>
            <div className="grid grid-2">
              <div>
                <h3>Token Claims</h3>
                <p><strong>Subject:</strong> {tokenInfo.decoded.sub}</p>
                <p><strong>Employee ID:</strong> {tokenInfo.decoded.employee_id}</p>
                <p><strong>Client ID:</strong> {tokenInfo.decoded.client_id}</p>
                <p><strong>Scope:</strong> {tokenInfo.decoded.scope}</p>
                <p><strong>Issued At:</strong> {formatDate(tokenInfo.decoded.iat)}</p>
                <p><strong>Expires At:</strong> {formatDate(tokenInfo.decoded.exp)}</p>
                <p><strong>Issuer:</strong> {tokenInfo.decoded.iss}</p>
                <p><strong>Audience:</strong> {tokenInfo.decoded.aud}</p>
              </div>
              
              <div>
                <h3>Token Verification</h3>
                <p><strong>Signature Valid:</strong> 
                  <span style={{ 
                    color: tokenInfo.verification.valid ? 'green' : 'red',
                    fontWeight: 'bold'
                  }}>
                    {tokenInfo.verification.valid ? '✅ Valid' : '❌ Invalid'}
                  </span>
                </p>
                {!tokenInfo.verification.valid && (
                  <p><strong>Error:</strong> {tokenInfo.verification.error}</p>
                )}
                <p><strong>Algorithm:</strong> RS256</p>
                <p><strong>Verification Method:</strong> Public Key (JWKS)</p>
              </div>
            </div>

            <div className="mt-3">
              <h3>Raw Token (First 100 characters)</h3>
              <div className="code-block">
                {tokenInfo.raw.substring(0, 100)}...
              </div>
              <p className="text-muted mt-1">
                Full token is stored securely and used for API authentication
              </p>
            </div>
          </div>
        ) : (
          <p>No token information available</p>
        )}
      </div>

      {/* OAuth Configuration */}
      <div className="card">
        <h2>⚙️ Client Configuration</h2>
        <div className="grid grid-2">
          <div>
            <h3>OAuth Settings</h3>
            <p><strong>Client ID:</strong> {OAUTH_CONFIG.clientId}</p>
            <p><strong>Redirect URI:</strong> {OAUTH_CONFIG.redirectUri}</p>
            <p><strong>Scope:</strong> {OAUTH_CONFIG.scope}</p>
            <p><strong>Auth Server:</strong> {OAUTH_CONFIG.authServerUrl}</p>
          </div>
          
          <div>
            <h3>Supported Features</h3>
            <ul>
              <li>✅ RS256 JWT Token Verification</li>
              <li>✅ Automatic Token Refresh</li>
              <li>✅ JWKS Public Key Discovery</li>
              <li>✅ OAuth 2.0 Password Grant</li>
              <li>✅ OAuth 2.0 Authorization Code Grant</li>
              <li>✅ Secure Token Storage</li>
              <li>✅ Protected API Calls</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}

export default DashboardPage

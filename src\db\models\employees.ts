import {
  ColumnType,
  Generated,
  Insertable,
  Selectable,
  Updateable,
} from 'kysely'

export interface EmployeesTable {
  employee_id: Generated<number>
  status: number // tinyint(1)
  first_name: string
  last_name: string
  department: string
  short_name: string | null
  nickname: string | null
  email: string | null
  work_area_id: number | null
  dui: string
  nit: string
  isss: string
  employment_date: ColumnType<Date, string, string>
  image: string | null
  employee_contact: string | null
  emergency_number: string | null
  gender: string | null
  barcode: Generated<number>
  created_at: ColumnType<Date, string | undefined, never>
  updated_at: ColumnType<Date, string | undefined, string | undefined>
  emp_barcode: number | null
  rfid_code: number | null
  section: string | null
  title: string | null
  departments_id: number | null
  title_id: number | null
  // New OAuth-related columns
  password_hash: string | null
  username: string | null
  last_login_at: ColumnType<Date, string | undefined, string | undefined> | null
  failed_login_attempts: number
  locked_until: ColumnType<Date, string | undefined, string | undefined> | null
  is_admin: boolean
}

// Export types for Employees
export type Employee = Selectable<EmployeesTable>
export type NewEmployee = Insertable<EmployeesTable>
export type EmployeeUpdate = Updateable<EmployeesTable>

import type { <PERSON>ys<PERSON> } from 'kysely'

export async function up(db: <PERSON><PERSON><PERSON><any>): Promise<void> {
  // Add PIN column to employees table
  await db.schema
    .alterTable('employees')
    .addColumn('pin', 'varchar(8)', (col) => col)
    .execute()

  console.log('Added pin column to employees table')
}

export async function down(db: <PERSON><PERSON><PERSON><any>): Promise<void> {
  // Remove the PIN column
  await db.schema
    .alterTable('employees')
    .dropColumn('pin')
    .execute()

  console.log('Removed pin column from employees table')
}

<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>VarPro Dashboard</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family:
          -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu,
          Cantarell, sans-serif;
        background: #f8f9fa;
        min-height: 100vh;
      }

      .header {
        background: white;
        border-bottom: 1px solid #e1e5e9;
        padding: 16px 24px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
      }

      .logo {
        font-size: 24px;
        font-weight: 700;
        color: #333;
      }

      .user-info {
        display: flex;
        align-items: center;
        gap: 16px;
      }

      .user-name {
        color: #333;
        font-weight: 500;
      }

      .logout-btn {
        background: #dc3545;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 6px;
        cursor: pointer;
        font-size: 14px;
        transition: background-color 0.2s ease;
      }

      .logout-btn:hover {
        background: #c82333;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 40px 24px;
      }

      .welcome-card {
        background: white;
        border-radius: 12px;
        padding: 32px;
        margin-bottom: 32px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      }

      .welcome-title {
        font-size: 28px;
        font-weight: 700;
        color: #333;
        margin-bottom: 8px;
      }

      .welcome-subtitle {
        color: #666;
        font-size: 16px;
      }

      .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 24px;
        margin-bottom: 32px;
      }

      .stat-card {
        background: white;
        border-radius: 12px;
        padding: 24px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        text-align: center;
      }

      .stat-icon {
        width: 48px;
        height: 48px;
        margin: 0 auto 16px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 24px;
      }

      .stat-value {
        font-size: 32px;
        font-weight: 700;
        color: #333;
        margin-bottom: 8px;
      }

      .stat-label {
        color: #666;
        font-size: 14px;
      }

      .actions-card {
        background: white;
        border-radius: 12px;
        padding: 32px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      }

      .actions-title {
        font-size: 20px;
        font-weight: 600;
        color: #333;
        margin-bottom: 24px;
      }

      .actions-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 16px;
      }

      .action-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 16px 24px;
        border-radius: 8px;
        cursor: pointer;
        font-size: 16px;
        font-weight: 500;
        transition:
          transform 0.2s ease,
          box-shadow 0.2s ease;
        text-decoration: none;
        display: inline-block;
        text-align: center;
      }

      .action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
      }

      .loading {
        text-align: center;
        padding: 40px;
      }

      .spinner {
        display: inline-block;
        width: 32px;
        height: 32px;
        border: 3px solid #f3f3f3;
        border-top: 3px solid #667eea;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 16px;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      .error {
        background: #fee;
        color: #c53030;
        padding: 16px;
        border-radius: 8px;
        margin-bottom: 24px;
        border: 1px solid #fed7d7;
      }
    </style>
  </head>
  <body>
    <div class="header">
      <div class="logo">VarPro</div>
      <div class="user-info">
        <span class="user-name" id="userName">Loading...</span>
        <button class="logout-btn" onclick="logout()">Logout</button>
      </div>
    </div>

    <div class="container">
      <div id="loading" class="loading">
        <div class="spinner"></div>
        <p>Loading dashboard...</p>
      </div>

      <div id="error" class="error" style="display: none"></div>

      <div id="dashboard" style="display: none">
        <div class="welcome-card">
          <h1 class="welcome-title">Welcome back!</h1>
          <p class="welcome-subtitle">
            Here's what's happening with your account today.
          </p>
        </div>

        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-icon">👤</div>
            <div class="stat-value" id="userCount">-</div>
            <div class="stat-label">Active Sessions</div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">🔑</div>
            <div class="stat-value" id="tokenCount">-</div>
            <div class="stat-label">Access Tokens</div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">📊</div>
            <div class="stat-value" id="requestCount">-</div>
            <div class="stat-label">API Requests</div>
          </div>
        </div>

        <div class="actions-card">
          <h2 class="actions-title">Quick Actions</h2>
          <div class="actions-grid">
            <button class="action-btn" onclick="viewProfile()">
              View Profile
            </button>
            <button class="action-btn" onclick="changePassword()">
              Change Password
            </button>
            <a href="/admin" class="action-btn">Admin Panel</a>
            <button class="action-btn" onclick="viewTokens()">
              Manage Tokens
            </button>
          </div>
        </div>
      </div>
    </div>

    <script>
      const OAUTH_CONFIG = {
        serverUrl: 'http://localhost:3230',
      }

      let currentUser = null

      // Check authentication and load user data
      async function loadDashboard() {
        const token = localStorage.getItem('access_token')

        if (!token) {
          window.location.href = '/login.html'
          return
        }

        try {
          // Get current user info
          const response = await fetch(`${OAUTH_CONFIG.serverUrl}/oauth/me`, {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          })

          if (!response.ok) {
            throw new Error('Failed to load user data')
          }

          const userData = await response.json()
          currentUser = userData

          // Update UI
          document.getElementById('userName').textContent = userData.first_name
            ? `${userData.first_name} ${userData.last_name}`
            : userData.username

          // Show dashboard
          document.getElementById('loading').style.display = 'none'
          document.getElementById('dashboard').style.display = 'block'

          // Load stats (mock data for now)
          loadStats()
        } catch (error) {
          console.error('Dashboard load error:', error)
          showError('Failed to load dashboard. Please try logging in again.')

          // Clear invalid token
          localStorage.removeItem('access_token')
          localStorage.removeItem('refresh_token')

          setTimeout(() => {
            window.location.href = '/login.html'
          }, 2000)
        }
      }

      // Load dashboard statistics
      function loadStats() {
        // Mock data - in a real app, these would come from API calls
        document.getElementById('userCount').textContent = '1'
        document.getElementById('tokenCount').textContent = '1'
        document.getElementById('requestCount').textContent = '0'
      }

      // Show error message
      function showError(message) {
        const errorDiv = document.getElementById('error')
        errorDiv.textContent = message
        errorDiv.style.display = 'block'
        document.getElementById('loading').style.display = 'none'
      }

      // Logout function
      async function logout() {
        const token = localStorage.getItem('access_token')

        try {
          // Call logout endpoint
          await fetch(`${OAUTH_CONFIG.serverUrl}/oauth/revoke`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${token}`,
            },
            body: JSON.stringify({
              token: token,
            }),
          })
        } catch (error) {
          console.error('Logout error:', error)
        }

        // Clear local storage
        localStorage.removeItem('access_token')
        localStorage.removeItem('refresh_token')

        // Redirect to login
        window.location.href = '/login.html'
      }

      // Quick action functions
      function viewProfile() {
        alert('Profile view functionality would be implemented here.')
      }

      function changePassword() {
        alert('Change password functionality would be implemented here.')
      }

      function viewTokens() {
        alert('Token management functionality would be implemented here.')
      }

      // Load dashboard on page load
      window.addEventListener('load', loadDashboard)
    </script>
  </body>
</html>

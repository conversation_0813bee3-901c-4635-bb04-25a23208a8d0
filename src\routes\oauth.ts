import { Request, Router } from 'express'
import { StatusCodes } from 'http-status-codes'
import { authorize, revoke, token } from '../controllers/oauthController'
import {
  jwtHeaderAuthMiddleware,
  type AuthenticatedReponse,
} from '../middleware/jwtAuthMiddleware'
import { oauthClientService } from '../services/oauthClientService'

export const oAuthRouter = Router()

// OAuth 2.0 Authorization endpoint
// GET /oauth/authorize?response_type=code&client_id=...&redirect_uri=...&scope=...&state=...
oAuthRouter.get('/authorize', authorize)
// POST /oauth/authorize (for form submission)
oAuthRouter.post('/authorize', authorize)

// OAuth 2.0 Token endpoint
// POST /oauth/token
oAuthRouter.post('/token', token)

// OAuth 2.0 Token revocation endpoint
// POST /oauth/revoke
oAuthRouter.post('/revoke', revoke)

// Get client information from JWT token
oAuthRouter.get('/client', async (req: Request, res) => {
  // using the client_id from the query string return the client information for the login page
  const client_id = req.query.client_id as string | undefined
  if (!client_id) {
    res.status(StatusCodes.BAD_REQUEST).json({
      error: 'invalid_request',
      error_description: 'Missing client_id',
    })
    return
  }

  const client = await oauthClientService.getClientByClientId(client_id)
  if (!client) {
    res.status(StatusCodes.NOT_FOUND).json({
      error: 'not_found',
      error_description: 'Client not found',
    })
    return
  }

  // Return client information (excluding sensitive data)
  res.json({
    client_name: client.client_name,
    client_id: client.client_id,
  })
})

// Get employee information from JWT token
oAuthRouter.get('/me', jwtHeaderAuthMiddleware, (req, res) => {
  try {
    const tokenPayload = (res as AuthenticatedReponse).locals.tokenPayload

    if (!tokenPayload) {
      res.status(StatusCodes.BAD_REQUEST).json({
        error: 'invalid_token',
        error_description: 'Invalid token payload',
      })
      return
    }

    // Extract employee information from JWT token
    if (tokenPayload.employee) {
      res.json({
        employee_id: tokenPayload.employee.employee_id,
        first_name: tokenPayload.employee.first_name,
        last_name: tokenPayload.employee.last_name,
        nickname: tokenPayload.employee.nickname,
        full_name: `${tokenPayload.employee.first_name} ${tokenPayload.employee.last_name}`,
        display_name:
          tokenPayload.employee.nickname || tokenPayload.employee.first_name,
        // Include token metadata
        client_id: tokenPayload.client_id,
        scope: tokenPayload.scope,
        expires_at: new Date(tokenPayload.exp * 1000).toISOString(),
        issued_at: new Date(tokenPayload.iat * 1000).toISOString(),
      })
    } else if (tokenPayload.employee_id) {
      // Fallback for older tokens that might not have embedded employee info
      res.json({
        employee_id: tokenPayload.employee_id,
        client_id: tokenPayload.client_id,
        scope: tokenPayload.scope,
        expires_at: new Date(tokenPayload.exp * 1000).toISOString(),
        issued_at: new Date(tokenPayload.iat * 1000).toISOString(),
        message:
          'Employee details not embedded in token. Consider refreshing your token.',
      })
    } else {
      res.status(StatusCodes.BAD_REQUEST).json({
        error: 'no_employee_info',
        error_description: 'Token does not contain employee information',
      })
    }
  } catch (error) {
    console.error('Error getting employee info:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      error: 'server_error',
      error_description: 'Failed to retrieve employee information',
    })
  }
})

import { Request, Response } from 'express'
import { StatusCodes } from 'http-status-codes'
import { employeeService } from '../services/employeeService'
import { oauthTokenService } from '../services/oauthTokenService'

interface AuthenticatedRequest extends Request {
  employee?: {
    employee_id: number
    client_id: string
    scope?: string
  }
}

export const getCurrentUser = async (
  req: AuthenticatedRequest,
  res: Response
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        error: 'unauthorized',
        message: 'Missing or invalid authorization header',
      })
      return
    }

    const token = authHeader.substring(7)
    const payload = await oauthTokenService.validateAccessToken(token)

    if (!payload || !payload.employee_id) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        error: 'unauthorized',
        message: 'Invalid or expired token',
      })
      return
    }

    const employee = await employeeService.getEmployeeById(payload.employee_id)
    if (!employee) {
      res.status(StatusCodes.NOT_FOUND).json({
        error: 'not_found',
        message: 'Employee not found',
      })
      return
    }

    res.json(employee)
  } catch (error) {
    console.error('Error getting current employee:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      error: 'server_error',
      message: 'Failed to retrieve current employee',
    })
  }
}

export const getAllUsers = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const employees = await employeeService.getAllEmployees()
    res.json(employees)
  } catch (error) {
    console.error('Error getting employees:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      error: 'server_error',
      message: 'Failed to retrieve employees',
    })
  }
}

export const getUserById = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const id = parseInt(req.params.id)
    if (isNaN(id)) {
      res.status(StatusCodes.BAD_REQUEST).json({
        error: 'invalid_request',
        message: 'Invalid employee ID',
      })
      return
    }

    const employee = await employeeService.getEmployeeById(id)
    if (!employee) {
      res.status(StatusCodes.NOT_FOUND).json({
        error: 'not_found',
        message: 'Employee not found',
      })
      return
    }

    res.json(employee)
  } catch (error) {
    console.error('Error getting employee:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      error: 'server_error',
      message: 'Failed to retrieve employee',
    })
  }
}

export const setupOAuthAccess = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { employee_id, username, password } = req.body

    if (!employee_id || !password) {
      res.status(StatusCodes.BAD_REQUEST).json({
        error: 'invalid_request',
        message: 'Employee ID and password are required',
      })
      return
    }

    // Check if employee exists (must be created by HR/employee management system)
    const existingEmployee = await employeeService.getEmployeeById(employee_id)
    if (!existingEmployee) {
      res.status(StatusCodes.NOT_FOUND).json({
        error: 'not_found',
        message:
          'Employee not found. Employee must be created by HR system first.',
      })
      return
    }

    // Set up OAuth access: password and optional username
    await employeeService.updateEmployeePassword(employee_id, password)
    if (username) {
      const usernameResult = await employeeService.setUsername(
        employee_id,
        username
      )
      if (!usernameResult.success) {
        res.status(StatusCodes.BAD_REQUEST).json({
          error: 'invalid_request',
          message: usernameResult.error || 'Failed to set username',
        })
        return
      }
    }

    const updatedEmployee = await employeeService.getEmployeeById(employee_id)
    res.status(StatusCodes.CREATED).json({
      message: 'OAuth access configured successfully',
      employee: updatedEmployee,
    })
  } catch (error) {
    console.error('Error setting up employee OAuth access:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      error: 'server_error',
      message: 'Failed to set up employee OAuth access',
    })
  }
}

export const updateUser = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const id = parseInt(req.params.id)
    if (isNaN(id)) {
      res.status(StatusCodes.BAD_REQUEST).json({
        error: 'invalid_request',
        message: 'Invalid employee ID',
      })
      return
    }

    const { username, password } = req.body

    // Check if employee exists
    const existingEmployee = await employeeService.getEmployeeById(id)
    if (!existingEmployee) {
      res.status(StatusCodes.NOT_FOUND).json({
        error: 'not_found',
        message: 'Employee not found',
      })
      return
    }

    // Update username if provided
    if (username) {
      const usernameResult = await employeeService.setUsername(id, username)
      if (!usernameResult.success) {
        res.status(StatusCodes.BAD_REQUEST).json({
          error: 'invalid_request',
          message: usernameResult.error || 'Failed to update username',
        })
        return
      }
    }

    // Update password if provided
    if (password) {
      await employeeService.updateEmployeePassword(id, password)
    }

    const updatedEmployee = await employeeService.getEmployeeById(id)
    if (!updatedEmployee) {
      res.status(StatusCodes.NOT_FOUND).json({
        error: 'not_found',
        message: 'Employee not found',
      })
      return
    }

    res.json(updatedEmployee)
  } catch (error) {
    console.error('Error updating employee:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      error: 'server_error',
      message: 'Failed to update employee',
    })
  }
}

export const removeUsernameAndPasswordFromUser = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const id = parseInt(req.params.id)
    if (isNaN(id)) {
      res.status(StatusCodes.BAD_REQUEST).json({
        error: 'invalid_request',
        message: 'Invalid employee ID',
      })
      return
    }

    // For OAuth server, we don't actually delete employees, just remove OAuth access
    // Clear password and username to disable OAuth access
    await employeeService.updateEmployee(id, {
      password_hash: null,
      username: null,
    })

    res.status(StatusCodes.NO_CONTENT).send()
  } catch (error) {
    console.error('Error removing employee OAuth access:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      error: 'server_error',
      message: 'Failed to remove employee OAuth access',
    })
  }
}

export const changePassword = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const id = parseInt(req.params.id)
    if (isNaN(id)) {
      res.status(StatusCodes.BAD_REQUEST).json({
        error: 'invalid_request',
        message: 'Invalid employee ID',
      })
      return
    }

    const { current_password, new_password } = req.body

    if (!current_password || !new_password) {
      res.status(StatusCodes.BAD_REQUEST).json({
        error: 'invalid_request',
        message: 'Current password and new password are required',
      })
      return
    }

    const success = await employeeService.changePassword(
      id,
      current_password,
      new_password
    )
    if (!success) {
      res.status(StatusCodes.BAD_REQUEST).json({
        error: 'invalid_request',
        message: 'Invalid current password or employee not found',
      })
      return
    }

    res.json({ message: 'Password changed successfully' })
  } catch (error) {
    console.error('Error changing password:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      error: 'server_error',
      message: 'Failed to change password',
    })
  }
}

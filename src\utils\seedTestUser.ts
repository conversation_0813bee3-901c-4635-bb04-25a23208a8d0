import bcrypt from 'bcrypt'
import { db } from '../db/database'
import { NewEmployee } from '../db/models/employees'

async function seedTestUser() {
  console.log('👤 Seeding test admin user...')

  try {
    // Check if admin user already exists
    const existingUser = await db
      .selectFrom('employees')
      .select(['employee_id', 'username'])
      .where('username', '=', 'admin')
      .executeTakeFirst()

    if (existingUser) {
      console.log('⏭️  Admin user already exists:', existingUser.username)
      return existingUser
    }

    // Hash the password
    const passwordHash = await bcrypt.hash('admin123', 10)

    const adminUser: NewEmployee = {
      status: 1, // Active
      first_name: 'Admin',
      last_name: 'User',
      department: 'IT',
      short_name: 'Admin',
      nickname: 'Admin',
      email: '<EMAIL>',
      work_area_id: null,
      dui: '00000000-0',
      nit: '0000-000000-000-0',
      isss: '000000000',
      employment_date: new Date().toISOString().split('T')[0],
      image: null,
      employee_contact: null,
      emergency_number: null,
      gender: null,
      emp_barcode: null,
      rfid_code: null,
      section: 'Administration',
      title: 'System Administrator',
      departments_id: null,
      title_id: null,
      password_hash: passwordHash,
      username: 'admin',
      last_login_at: null,
      failed_login_attempts: 0,
      locked_until: null,
      is_admin: true,
    }

    const result = await db
      .insertInto('employees')
      .values(adminUser)
      .executeTakeFirst()

    console.log('✅ Created admin user: admin / admin123')
    console.log('🔑 Employee ID:', result.insertId)

    return result
  } catch (error) {
    console.error('❌ Error seeding test user:', error)
    throw error
  }
}

// Run the seeding function if this script is executed directly
if (require.main === module) {
  seedTestUser()
    .then(() => {
      console.log('✨ User seeding completed successfully')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 User seeding failed:', error)
      process.exit(1)
    })
}

export { seedTestUser }

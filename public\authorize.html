<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>VarPro OAuth Authorization</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family:
          -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu,
          Cantarell, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px;
      }

      .auth-container {
        background: white;
        border-radius: 12px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        padding: 40px;
        width: 100%;
        max-width: 400px;
      }

      .logo {
        text-align: center;
        margin-bottom: 30px;
      }

      .logo h1 {
        color: #333;
        font-size: 28px;
        font-weight: 700;
        margin-bottom: 8px;
      }

      .logo p {
        color: #666;
        font-size: 14px;
      }

      .client-info {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 16px;
        margin-bottom: 24px;
      }

      .client-info h3 {
        color: #333;
        font-size: 16px;
        margin-bottom: 8px;
      }

      .client-info p {
        color: #666;
        font-size: 14px;
        margin-bottom: 4px;
      }

      .form-group {
        margin-bottom: 20px;
      }

      label {
        display: block;
        margin-bottom: 6px;
        color: #333;
        font-weight: 500;
        font-size: 14px;
      }

      input[type='text'],
      input[type='password'] {
        width: 100%;
        padding: 12px 16px;
        border: 2px solid #e1e5e9;
        border-radius: 8px;
        font-size: 16px;
        transition: border-color 0.3s ease;
        background: #f8f9fa;
      }

      input[type='text']:focus,
      input[type='password']:focus {
        outline: none;
        border-color: #667eea;
        background: white;
      }

      .auth-btn {
        width: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 14px;
        border-radius: 8px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition:
          transform 0.2s ease,
          box-shadow 0.2s ease;
        margin-top: 10px;
      }

      .auth-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
      }

      .auth-btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
      }

      .cancel-btn {
        width: 100%;
        background: #6c757d;
        color: white;
        border: none;
        padding: 12px;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        margin-top: 12px;
      }

      .cancel-btn:hover {
        background: #5a6268;
      }

      .error-message {
        background: #fee;
        color: #c53030;
        padding: 12px;
        border-radius: 8px;
        margin-bottom: 20px;
        font-size: 14px;
        border: 1px solid #fed7d7;
      }

      .loading {
        display: none;
        text-align: center;
        margin-top: 10px;
      }

      .loading.show {
        display: block;
      }

      .spinner {
        display: inline-block;
        width: 20px;
        height: 20px;
        border: 2px solid #f3f3f3;
        border-top: 2px solid #667eea;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-right: 8px;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }
    </style>
  </head>
  <body>
    <div class="auth-container">
      <div class="logo">
        <h1>VarPro</h1>
        <p>OAuth Authorization Server</p>
      </div>

      <div class="client-info">
        <h3 id="clientName">Loading...</h3>
        <p><strong>Client ID:</strong> <span id="clientId">-</span></p>
        <p>
          <strong>Requested Scope:</strong> <span id="requestedScope">-</span>
        </p>
        <p><strong>Redirect URI:</strong> <span id="redirectUri">-</span></p>
      </div>

      <div id="error-container"></div>

      <form id="authForm">
        <div class="form-group">
          <label for="username">Username</label>
          <input type="text" id="username" name="username" required />
        </div>

        <div class="form-group">
          <label for="password">Password</label>
          <input type="password" id="password" name="password" required />
        </div>

        <button type="submit" class="auth-btn" id="authBtn">Authorize</button>

        <div class="loading" id="loading">
          <div class="spinner"></div>
          <span>Authorizing...</span>
        </div>

        <button type="button" class="cancel-btn" onclick="cancelAuth()">
          Cancel
        </button>
      </form>
    </div>

    <script>
      // Parse URL parameters
      const urlParams = new URLSearchParams(window.location.search)
      const clientId = urlParams.get('client_id')
      const redirectUri = urlParams.get('redirect_uri')
      const scope = urlParams.get('scope') || 'read'
      const state = urlParams.get('state')
      const responseType = urlParams.get('response_type')

      // Validate required parameters
      if (!clientId || !redirectUri || !responseType) {
        showError('Missing required OAuth parameters')
      } else {
        // Display client information
        document.getElementById('clientId').textContent = clientId
        document.getElementById('redirectUri').textContent = redirectUri
        document.getElementById('requestedScope').textContent = scope

        // Load client name
        loadClientInfo()
      }

      // Load client information
      async function loadClientInfo() {
        try {
          const response = await fetch(`/clients?client_id=${clientId}`)
          if (response.ok) {
            const clients = await response.json()
            const client = clients.find((c) => c.client_id === clientId)
            if (client) {
              document.getElementById('clientName').textContent =
                client.client_name
            }
          }
        } catch (error) {
          console.error('Failed to load client info:', error)
        }
      }

      // Show error message
      function showError(message) {
        document.getElementById('error-container').innerHTML =
          `<div class="error-message">${message}</div>`
      }

      // Set loading state
      function setLoading(isLoading) {
        const authBtn = document.getElementById('authBtn')
        const loading = document.getElementById('loading')

        if (isLoading) {
          authBtn.disabled = true
          authBtn.textContent = ''
          loading.classList.add('show')
        } else {
          authBtn.disabled = false
          authBtn.textContent = 'Authorize'
          loading.classList.remove('show')
        }
      }

      // Handle authorization form submission
      document
        .getElementById('authForm')
        .addEventListener('submit', async (e) => {
          e.preventDefault()

          const username = document.getElementById('username').value
          const password = document.getElementById('password').value

          if (!username || !password) {
            showError('Please enter both username and password.')
            return
          }

          setLoading(true)

          try {
            // Submit authorization request
            const response = await fetch('/oauth/authorize', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
              body: new URLSearchParams({
                response_type: responseType,
                client_id: clientId,
                redirect_uri: redirectUri,
                scope: scope,
                state: state || '',
                username: username,
                password: password,
              }),
            })

            if (response.redirected) {
              // Follow the redirect
              window.location.href = response.url
            } else {
              const data = await response.json()
              if (!response.ok) {
                throw new Error(
                  data.error_description ||
                    data.message ||
                    'Authorization failed'
                )
              }

              // Handle successful authorization
              if (data.redirect_uri) {
                window.location.href = data.redirect_uri
              }
            }
          } catch (error) {
            console.error('Authorization error:', error)
            showError(
              error.message || 'Authorization failed. Please try again.'
            )
          } finally {
            setLoading(false)
          }
        })

      // Cancel authorization
      function cancelAuth() {
        const errorRedirect = new URL(redirectUri)
        errorRedirect.searchParams.set('error', 'access_denied')
        errorRedirect.searchParams.set(
          'error_description',
          'User denied authorization'
        )
        if (state) {
          errorRedirect.searchParams.set('state', state)
        }
        window.location.href = errorRedirect.toString()
      }
    </script>
  </body>
</html>

import { Request, Response } from 'express'
import { StatusCodes } from 'http-status-codes'
import { oauthClientService } from '../services/oauthClientService'

export const getAllClients = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const clients = await oauthClientService.getAllClients()
    res.json(clients)
  } catch (error) {
    console.error('Error getting OAuth clients:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      error: 'server_error',
      message: 'Failed to retrieve OAuth clients',
    })
  }
}

export const getClientById = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const id = parseInt(req.params.id)
    if (isNaN(id)) {
      res.status(StatusCodes.BAD_REQUEST).json({
        error: 'invalid_request',
        message: 'Invalid client ID',
      })
      return
    }

    const client = await oauthClientService.getClientById(id)
    if (!client) {
      res.status(StatusCodes.NOT_FOUND).json({
        error: 'not_found',
        message: 'OAuth client not found',
      })
      return
    }

    res.json(client)
  } catch (error) {
    console.error('Error getting OAuth client:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      error: 'server_error',
      message: 'Failed to retrieve OAuth client',
    })
  }
}

export const createClient = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const {
      client_name,
      redirect_uris,
      grant_types,
      scope,
      is_active,
      webhook_url,
      webhook_secret,
      webhook_events,
    } = req.body

    if (!client_name || !redirect_uris || !grant_types || !scope) {
      res.status(StatusCodes.BAD_REQUEST).json({
        error: 'invalid_request',
        message:
          'Missing required fields: client_name, redirect_uris, grant_types, scope',
      })
      return
    }

    if (!Array.isArray(redirect_uris) || !Array.isArray(grant_types)) {
      res.status(StatusCodes.BAD_REQUEST).json({
        error: 'invalid_request',
        message: 'redirect_uris and grant_types must be arrays',
      })
      return
    }

    if (webhook_events && !Array.isArray(webhook_events)) {
      res.status(StatusCodes.BAD_REQUEST).json({
        error: 'invalid_request',
        message: 'webhook_events must be an array',
      })
      return
    }

    const client = await oauthClientService.createClient({
      client_name,
      redirect_uris,
      grant_types,
      scope,
      is_active,
      webhook_url,
      webhook_secret,
      webhook_events,
    })

    res.status(StatusCodes.CREATED).json(client)
  } catch (error) {
    console.error('Error creating OAuth client:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      error: 'server_error',
      message: 'Failed to create OAuth client',
    })
  }
}

export const updateClient = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const id = parseInt(req.params.id)
    if (isNaN(id)) {
      res.status(StatusCodes.BAD_REQUEST).json({
        error: 'invalid_request',
        message: 'Invalid client ID',
      })
      return
    }

    const {
      client_name,
      redirect_uris,
      grant_types,
      scope,
      is_active,
      webhook_url,
      webhook_secret,
      webhook_events,
    } = req.body

    if (redirect_uris && !Array.isArray(redirect_uris)) {
      res.status(StatusCodes.BAD_REQUEST).json({
        error: 'invalid_request',
        message: 'redirect_uris must be an array',
      })
      return
    }

    if (grant_types && !Array.isArray(grant_types)) {
      res.status(StatusCodes.BAD_REQUEST).json({
        error: 'invalid_request',
        message: 'grant_types must be an array',
      })
      return
    }

    if (webhook_events && !Array.isArray(webhook_events)) {
      res.status(StatusCodes.BAD_REQUEST).json({
        error: 'invalid_request',
        message: 'webhook_events must be an array',
      })
      return
    }

    const client = await oauthClientService.updateClient(id, {
      client_name,
      redirect_uris,
      grant_types,
      scope,
      is_active,
      webhook_url,
      webhook_secret,
      webhook_events,
    })

    if (!client) {
      res.status(StatusCodes.NOT_FOUND).json({
        error: 'not_found',
        message: 'OAuth client not found',
      })
      return
    }

    res.json(client)
  } catch (error) {
    console.error('Error updating OAuth client:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      error: 'server_error',
      message: 'Failed to update OAuth client',
    })
  }
}

export const deleteClient = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const id = parseInt(req.params.id)
    if (isNaN(id)) {
      res.status(StatusCodes.BAD_REQUEST).json({
        error: 'invalid_request',
        message: 'Invalid client ID',
      })
      return
    }

    const success = await oauthClientService.deleteClient(id)
    if (!success) {
      res.status(StatusCodes.NOT_FOUND).json({
        error: 'not_found',
        message: 'OAuth client not found',
      })
      return
    }

    res.status(StatusCodes.NO_CONTENT).send()
  } catch (error) {
    console.error('Error deleting OAuth client:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      error: 'server_error',
      message: 'Failed to delete OAuth client',
    })
  }
}

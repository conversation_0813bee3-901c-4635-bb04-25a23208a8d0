import React, { useEffect, useState } from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext.jsx'
import LoadingSpinner from '../components/LoadingSpinner.jsx'

const CallbackPage = () => {
  const [searchParams] = useSearchParams()
  const navigate = useNavigate()
  const { handleAuthCallback } = useAuth()
  const [status, setStatus] = useState('processing') // 'processing', 'success', 'error'
  const [message, setMessage] = useState('')

  useEffect(() => {
    const processCallback = async () => {
      try {
        const code = searchParams.get('code')
        const state = searchParams.get('state')
        const error = searchParams.get('error')
        const errorDescription = searchParams.get('error_description')

        if (error) {
          setStatus('error')
          setMessage(errorDescription || `OAuth error: ${error}`)
          return
        }

        if (!code) {
          setStatus('error')
          setMessage('No authorization code received')
          return
        }

        setMessage('Processing authorization code...')

        const result = await handleAuthCallback(code, state)

        if (result.success) {
          setStatus('success')
          setMessage('Login successful! Redirecting...')
          setTimeout(() => {
            navigate('/dashboard')
          }, 2000)
        } else {
          setStatus('error')
          setMessage(result.error || 'Authorization failed')
        }
      } catch (err) {
        setStatus('error')
        setMessage('An unexpected error occurred')
        console.error('Callback processing error:', err)
      }
    }

    processCallback()
  }, [searchParams, handleAuthCallback, navigate])

  const handleRetry = () => {
    navigate('/login')
  }

  if (status === 'processing') {
    return <LoadingSpinner message={message} />
  }

  return (
    <div className="container">
      <div className="card" style={{ maxWidth: '500px', margin: '40px auto' }}>
        <div className="text-center">
          {status === 'success' ? (
            <>
              <div className="alert alert-success">
                <h2>✅ Authentication Successful!</h2>
                <p>{message}</p>
              </div>
              <div className="mt-3">
                <p>You will be redirected to the dashboard shortly...</p>
                <button 
                  onClick={() => navigate('/dashboard')} 
                  className="btn"
                >
                  Go to Dashboard Now
                </button>
              </div>
            </>
          ) : (
            <>
              <div className="alert alert-error">
                <h2>❌ Authentication Failed</h2>
                <p>{message}</p>
              </div>
              <div className="mt-3">
                <button onClick={handleRetry} className="btn">
                  Try Again
                </button>
              </div>
            </>
          )}
        </div>

        <div className="mt-3">
          <h3>What happened?</h3>
          <div className="text-left">
            {status === 'success' ? (
              <ul>
                <li>✅ Authorization code received from OAuth server</li>
                <li>✅ Code exchanged for access and refresh tokens</li>
                <li>✅ Tokens verified using RS256 public key</li>
                <li>✅ User information retrieved from protected endpoint</li>
                <li>✅ Authentication state updated in application</li>
              </ul>
            ) : (
              <ul>
                <li>❌ Authorization code flow failed</li>
                <li>🔍 Check OAuth server configuration</li>
                <li>🔍 Verify client credentials</li>
                <li>🔍 Ensure redirect URI matches configuration</li>
              </ul>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default CallbackPage

import axios from 'axios'
import { jwtDecode } from 'jwt-decode'

const OAUTH_SERVER_URL = 'http://localhost:3230'
const CLIENT_SERVER_URL = 'http://localhost:3001'

// Test credentials
const TEST_CREDENTIALS = {
  username: 'william de jesus.rivera marroquin',
  password: 'admin123',
  client_id: '07b2f434-ec60-4cac-aa53-f17008ab5e6d',
  client_secret:
    '6eac4814cddc514c84cffaf691aae3b3d7dd83b12c56a3ea93f3aa226c0576fa',
}

async function testJWTEmployeeInfo() {
  try {
    console.log('🧪 Testing JWT Token with Embedded Employee Information...\n')

    // Test 1: Get OAuth token
    console.log('1. Getting OAuth token with employee information...')
    const tokenResponse = await axios.post(`${OAUTH_SERVER_URL}/oauth/token`, {
      grant_type: 'password',
      username: TEST_CREDENTIALS.username,
      password: TEST_CREDENTIALS.password,
      client_id: TEST_CREDENTIALS.client_id,
      client_secret: TEST_CREDENTIALS.client_secret,
      scope: 'read write',
    })

    const accessToken = tokenResponse.data.access_token
    console.log('✅ OAuth token obtained')
    console.log(`   Token type: ${tokenResponse.data.token_type}`)
    console.log(`   Expires in: ${tokenResponse.data.expires_in} seconds`)

    // Test 2: Decode JWT token to see employee information
    console.log('\n2. Decoding JWT token to inspect employee information...')
    const decodedToken = jwtDecode(accessToken)
    console.log('✅ JWT token decoded')
    console.log(`   Subject: ${decodedToken.sub}`)
    console.log(`   Employee ID: ${decodedToken.employee_id}`)

    if (decodedToken.employee) {
      console.log('✅ Employee information found in JWT token:')
      console.log(`   Employee ID: ${decodedToken.employee.employee_id}`)
      console.log(`   First Name: ${decodedToken.employee.first_name}`)
      console.log(`   Last Name: ${decodedToken.employee.last_name}`)
      console.log(`   Nickname: ${decodedToken.employee.nickname || 'None'}`)
    } else {
      console.log('❌ No employee information found in JWT token')
    }

    // Test 3: Test client server with JWT token
    console.log('\n3. Testing client server with JWT token...')
    const serverInfoResponse = await axios.get(
      `${CLIENT_SERVER_URL}/api/info`,
      {
        headers: { Authorization: `Bearer ${accessToken}` },
      }
    )

    console.log('✅ Client server responded')
    console.log(`   Authenticated: ${serverInfoResponse.data.authenticated}`)

    if (serverInfoResponse.data.employee) {
      console.log('✅ Employee information extracted by client server:')
      console.log(
        `   Employee ID: ${serverInfoResponse.data.employee.employee_id}`
      )
      console.log(
        `   First Name: ${serverInfoResponse.data.employee.first_name}`
      )
      console.log(`   Last Name: ${serverInfoResponse.data.employee.last_name}`)
      console.log(`   Full Name: ${serverInfoResponse.data.employee.full_name}`)
      console.log(
        `   Display Name: ${serverInfoResponse.data.employee.display_name}`
      )
      console.log(`   Data Source: ${serverInfoResponse.data.employee.source}`)
    } else {
      console.log('❌ No employee information extracted by client server')
    }

    // Test 4: Test protected endpoint
    console.log('\n4. Testing protected endpoint with JWT employee info...')
    const profileResponse = await axios.get(
      `${CLIENT_SERVER_URL}/api/profile`,
      {
        headers: { Authorization: `Bearer ${accessToken}` },
      }
    )

    console.log('✅ Protected endpoint accessed')
    console.log(`   Employee: ${profileResponse.data.employee.full_name}`)
    console.log(
      `   Data Source: ${profileResponse.data.employee.data_source || 'unknown'}`
    )

    // Test 5: Test dashboard endpoint
    console.log('\n5. Testing dashboard endpoint...')
    const dashboardResponse = await axios.get(
      `${CLIENT_SERVER_URL}/api/dashboard`,
      {
        headers: { Authorization: `Bearer ${accessToken}` },
      }
    )

    console.log('✅ Dashboard data retrieved')
    console.log(
      `   Welcome: ${dashboardResponse.data.dashboard.welcome_message}`
    )
    console.log(
      `   Employee: ${dashboardResponse.data.dashboard.employee_info.full_name}`
    )
    console.log(
      `   Data Source: ${dashboardResponse.data.dashboard.employee_info.data_source}`
    )

    console.log('\n🎉 All JWT employee information tests passed!')
    console.log('\n📋 Test Summary:')
    console.log('   ✅ JWT token contains embedded employee information')
    console.log('   ✅ Client server extracts employee info from JWT')
    console.log(
      '   ✅ No additional OAuth server calls needed for basic employee data'
    )
    console.log('   ✅ Protected endpoints work with JWT employee info')
    console.log('   ✅ Performance improved by eliminating extra API calls')
  } catch (error) {
    console.error('❌ Test failed:', error.message)
    if (error.response) {
      console.error('Response status:', error.response.status)
      console.error('Response data:', error.response.data)
    }
    process.exit(1)
  }
}

testJWTEmployeeInfo()

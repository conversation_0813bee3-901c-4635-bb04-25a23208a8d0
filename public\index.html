<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VarPro OAuth Server</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 48px;
            width: 100%;
            max-width: 500px;
            text-align: center;
        }

        .logo {
            margin-bottom: 32px;
        }

        .logo h1 {
            color: #333;
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .logo p {
            color: #666;
            font-size: 16px;
        }

        .description {
            margin-bottom: 40px;
            color: #555;
            line-height: 1.6;
        }

        .options {
            display: grid;
            gap: 20px;
            margin-bottom: 32px;
        }

        .option-card {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 24px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .option-card:hover {
            border-color: #667eea;
            background: #f0f4ff;
            transform: translateY(-2px);
        }

        .option-card h3 {
            color: #333;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .option-card p {
            color: #666;
            font-size: 14px;
            margin-bottom: 16px;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }

        .btn.secondary {
            background: #6c757d;
        }

        .btn.secondary:hover {
            box-shadow: 0 8px 20px rgba(108, 117, 125, 0.3);
        }

        .footer {
            text-align: center;
            padding-top: 24px;
            border-top: 1px solid #e9ecef;
        }

        .footer p {
            color: #666;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">
            <h1>VarPro</h1>
            <p>OAuth 2.0 Authentication Server</p>
        </div>

        <div class="description">
            <p>Welcome to the VarPro OAuth authentication system. Choose your access level below.</p>
        </div>

        <div class="options">
            <div class="option-card" onclick="window.location.href='/login.html'">
                <h3>User Login</h3>
                <p>Standard user authentication for accessing protected resources</p>
                <a href="/login.html" class="btn">Sign In</a>
            </div>

            <div class="option-card" onclick="window.location.href='/admin'">
                <h3>Admin Panel</h3>
                <p>Administrative interface for managing users, clients, and system settings</p>
                <a href="/admin" class="btn secondary">Admin Access</a>
            </div>
        </div>

        <div class="footer">
            <p>&copy; 2024 VarPro. All rights reserved.</p>
        </div>
    </div>

    <script>
        // Check if user is already logged in and redirect appropriately
        window.addEventListener('load', () => {
            const token = localStorage.getItem('access_token');
            if (token) {
                // Check if this is an admin token (in a real app, you'd validate this properly)
                const currentPath = window.location.pathname;
                if (currentPath === '/' || currentPath === '/index.html') {
                    // Redirect to dashboard if already logged in
                    window.location.href = '/dashboard.html';
                }
            }
        });
    </script>
</body>
</html>

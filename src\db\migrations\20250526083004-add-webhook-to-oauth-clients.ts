import { sql, type <PERSON><PERSON><PERSON> } from 'kysely'

export async function up(db: <PERSON><PERSON><PERSON><any>): Promise<void> {
  // Add all webhook columns if none exist
  await db.schema
    .alterTable('oauth_clients')
    .addColumn('webhook_url', 'varchar(500)')
    .addColumn('webhook_secret', 'varchar(255)')
    .addColumn('webhook_events', 'json')
    .execute()
  // await sql`
  //   ALTER TABLE oauth_clients
  //   ADD COLUMN webhook_url VARCHAR(500) NULL,
  //   ADD COLUMN webhook_secret VARCHAR(255) NULL,
  //   ADD COLUMN webhook_events JSON NULL
  // `.execute(db)
  console.log('Added webhook columns to oauth_clients table')
}

export async function down(db: Kysely<any>): Promise<void> {
  // Remove the webhook columns
  db.schema
    .alterTable('oauth_clients')
    .dropColumn('webhook_url')
    .dropColumn('webhook_secret')
    .dropColumn('webhook_events')
    .execute()
  // await sql`
  //   ALTER TABLE oauth_clients
  //   DROP COLUMN webhook_url,
  //   DROP COLUMN webhook_secret,
  //   DROP COLUMN webhook_events
  // `.execute(db)
  console.log('Removed webhook columns from oauth_clients table')
}

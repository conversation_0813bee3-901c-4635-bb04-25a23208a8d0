import axios from 'axios'
import { db } from '../db/database'

/**
 * CLI utility to test username validation through API endpoints
 */
export async function testApiUsernameValidation() {
  try {
    console.log('🌐 Testing username validation through API endpoints...')
    console.log('')

    const baseUrl = 'http://localhost:3230'
    
    // Test cases for API username validation
    const testCases = [
      { 
        username: 'validuser123', 
        expected: 'success', 
        description: 'Valid username with letters and numbers' 
      },
      { 
        username: '<EMAIL>', 
        expected: 'error', 
        description: 'Username with @ character (should fail)',
        expectedError: 'Username cannot contain @ character'
      },
      { 
        username: '12345', 
        expected: 'error', 
        description: 'Username with only numbers (should fail)',
        expectedError: 'Username must contain at least 1 alphabetic character'
      },
      { 
        username: 'user_name_api', 
        expected: 'success', 
        description: 'Username with underscore and letters' 
      },
    ]

    console.log('Testing API username validation:')
    console.log('✅ = Expected result, ❌ = Unexpected result')
    console.log('')

    let passedTests = 0
    let totalTests = testCases.length

    for (const testCase of testCases) {
      try {
        console.log(`Testing: "${testCase.username}" - ${testCase.description}`)
        
        // Test creating a user with the username
        const response = await axios.post(`${baseUrl}/api/users`, {
          employee_id: 1, // Use existing employee
          username: testCase.username,
          password: 'testPassword123'
        })

        if (testCase.expected === 'success') {
          if (response.status === 201) {
            console.log('✅ API accepted valid username')
            passedTests++
          } else {
            console.log(`❌ Expected success but got status: ${response.status}`)
          }
        } else {
          console.log(`❌ Expected error but API accepted username (status: ${response.status})`)
        }
        
      } catch (error: any) {
        if (testCase.expected === 'error') {
          if (error.response && error.response.status === 400) {
            const errorMessage = error.response.data.message
            console.log(`✅ API correctly rejected username: ${errorMessage}`)
            
            if (testCase.expectedError && errorMessage.includes(testCase.expectedError)) {
              console.log('✅ Error message matches expected validation error')
            } else if (testCase.expectedError) {
              console.log(`⚠️  Error message doesn't match expected: "${testCase.expectedError}"`)
            }
            passedTests++
          } else {
            console.log(`❌ Expected validation error but got: ${error.response?.status || error.message}`)
          }
        } else {
          console.log(`❌ Expected success but got error: ${error.response?.data?.message || error.message}`)
        }
      }
      
      console.log('')
    }

    console.log(`📊 API Test Results: ${passedTests}/${totalTests} tests passed`)
    
    if (passedTests === totalTests) {
      console.log('🎉 All API username validation tests passed!')
    } else {
      console.log('⚠️  Some API tests failed. Please review the API validation.')
    }

  } catch (error) {
    console.error('💥 Failed to run API username validation tests:', error)
    if (error instanceof Error) {
      console.error('   Error details:', error.message)
    }
    console.log('')
    console.log('💡 Make sure the server is running on http://localhost:3230')
    console.log('   Run: npm run dev')
  } finally {
    // Close the database connection
    try {
      await db.destroy()
    } catch (error) {
      console.error('Warning: Failed to close database connection:', error)
    }
    process.exit(0)
  }
}

// If this file is run directly, execute the tests
if (require.main === module) {
  testApiUsernameValidation()
}

import {
  jwtAdminAuthMiddleware,
  jwtHeaderAuthMiddleware,
} from '@app/middleware/jwtAuthMiddleware'
import express, { NextFunction, Request, Response } from 'express'
import { v4 as uuidv4 } from 'uuid'
import { v1Router } from './v1/index'

export const apiRouter = express.Router()

export interface ApiResponse extends Response {
  locals: {
    request_uuid: string
  }
}

const requestLogger = (req: Request, res: Response, next: NextFunction) => {
  const startTime = Date.now()
  const requestUuid = uuidv4()
  res.locals.request_uuid = requestUuid

  console.log('Request UUID:', requestUuid)

  const originalSend = res.send
  // @ts-expect-error res.send being overwritten is correct
  res.send = function (responseData, ...rest) {
    if (responseData != null && typeof responseData === 'object') {
      // res.send will be called a second time with a string later so ignore for now
      return originalSend.apply(this, rest)
    }
    let isJson = false
    if (responseData && typeof responseData === 'string') {
      try {
        JSON.parse(responseData)
        isJson = true
        console.log('responseData parsed')
      } catch (error) {
        // Not JSON, no need to parse
      }
    }
    try {
      const endTime = Date.now()
      const duration = endTime - startTime
      const ip = (
        (req.headers['x-real-ip'] && Array.isArray(req.headers['x-real-ip'])
          ? req.headers['x-real-ip'].join(',')
          : req.headers['x-real-ip']) ||
        req.socket.remoteAddress ||
        req.ip ||
        ''
      )
        .split(',')[0]
        .trim()
      const saveData = {
        request_uuid: requestUuid,
        ip_address: ip,
        method: req.method,
        url: req.originalUrl,
        headers: JSON.stringify(req.headers),
        body: JSON.stringify(req.body),
        response: isJson ? responseData : JSON.stringify(responseData),
        status_code: res.statusCode,
        duration_ms: duration,
        //customer_app_id: res.locals?.customer_app_id,
      }
      // console.log('log data', saveData)

      // db.insertInto('api_requests').values(saveData).execute()
    } catch (error) {
      console.error('Error saving request log:', error)
    }

    originalSend.apply(this, rest)
  }

  next()
}

apiRouter.use(requestLogger)

apiRouter.use(express.json())

apiRouter.use(jwtHeaderAuthMiddleware)
apiRouter.use(jwtAdminAuthMiddleware)

apiRouter.use('/v1', v1Router)

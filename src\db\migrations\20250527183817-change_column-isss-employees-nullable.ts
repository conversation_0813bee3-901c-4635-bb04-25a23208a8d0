import type { <PERSON>ysely } from 'kysely'

// `any` is required here since migrations should be frozen in time. alternatively, keep a "snapshot" db interface.
export async function up(db: <PERSON><PERSON><PERSON><any>): Promise<void> {
  await db.schema
    .alterTable('employees')
    .modifyColumn('isss', 'varchar(255)', (col) => col.defaultTo(null))
    .execute()
}

// `any` is required here since migrations should be frozen in time. alternatively, keep a "snapshot" db interface.
export async function down(db: Kysely<any>): Promise<void> {
  await db.schema
    .alterTable('employees')
    .modifyColumn('isss', 'varchar(255)', (col) => col.defaultTo(''))
    .execute()
}

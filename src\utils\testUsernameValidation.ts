import { employeeService } from '../services/employeeService'
import { db } from '../db/database'

/**
 * CLI utility to test username validation rules
 */
export async function testUsernameValidation() {
  try {
    console.log('🧪 Testing username validation rules...')
    console.log('')

    // Test cases for username validation
    const testCases = [
      { username: 'validuser123', expected: true, description: 'Valid username with letters and numbers' },
      { username: '<EMAIL>', expected: false, description: 'Username with @ character (should fail)' },
      { username: '12345', expected: false, description: 'Username with only numbers (should fail)' },
      { username: 'user_name', expected: true, description: 'Username with underscore and letters' },
      { username: 'user-name', expected: true, description: 'Username with dash and letters' },
      { username: '@username', expected: false, description: 'Username starting with @ (should fail)' },
      { username: 'username@', expected: false, description: 'Username ending with @ (should fail)' },
      { username: '', expected: false, description: 'Empty username (should fail)' },
      { username: '   ', expected: false, description: 'Whitespace only username (should fail)' },
      { username: 'a', expected: true, description: 'Single letter username' },
      { username: 'A1', expected: true, description: 'Mixed case with number' },
      { username: '!@#$%', expected: false, description: 'Special characters only (should fail)' },
      { username: 'user!name', expected: true, description: 'Username with exclamation mark and letters' },
    ]

    console.log('Testing username validation rules:')
    console.log('✅ = Expected result, ❌ = Unexpected result')
    console.log('')

    let passedTests = 0
    let totalTests = testCases.length

    for (const testCase of testCases) {
      try {
        // We'll test this by trying to set a username for employee ID 1
        // (assuming it exists from seeding)
        const result = await employeeService.setUsername(1, testCase.username)
        
        const passed = (result.success === testCase.expected)
        const icon = passed ? '✅' : '❌'
        
        console.log(`${icon} "${testCase.username}" - ${testCase.description}`)
        if (!passed) {
          console.log(`   Expected: ${testCase.expected ? 'success' : 'failure'}, Got: ${result.success ? 'success' : 'failure'}`)
          if (result.error) {
            console.log(`   Error: ${result.error}`)
          }
        } else if (!result.success && result.error) {
          console.log(`   Error: ${result.error}`)
        }
        
        if (passed) passedTests++
        
      } catch (error) {
        console.log(`❌ "${testCase.username}" - ${testCase.description}`)
        console.log(`   Unexpected error: ${error}`)
      }
      
      console.log('')
    }

    console.log(`📊 Test Results: ${passedTests}/${totalTests} tests passed`)
    
    if (passedTests === totalTests) {
      console.log('🎉 All username validation tests passed!')
    } else {
      console.log('⚠️  Some tests failed. Please review the validation logic.')
    }

  } catch (error) {
    console.error('💥 Failed to run username validation tests:', error)
    if (error instanceof Error) {
      console.error('   Error details:', error.message)
    }
  } finally {
    // Close the database connection
    try {
      await db.destroy()
    } catch (error) {
      console.error('Warning: Failed to close database connection:', error)
    }
    process.exit(0)
  }
}

// If this file is run directly, execute the tests
if (require.main === module) {
  testUsernameValidation()
}

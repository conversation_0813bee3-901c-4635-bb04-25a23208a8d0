import { Router } from 'express'
import {
  getJ<PERSON><PERSON>,
  getPublic<PERSON><PERSON>,
  getServerMetadata,
} from '../controllers/jwksController'

export const wellKnownRouter = Router()

// JWKS endpoint (RFC 7517)
wellKnownRouter.get('/jwks.json', getJWKS)

// OAuth server metadata endpoint (RFC 8414)
wellKnownRouter.get('/oauth-authorization-server', getServerMetadata)

// Simple public key endpoint
wellKnownRouter.get('/public-key', getPublicKey)

import type { <PERSON>ys<PERSON> } from 'kysely'

// `any` is required here since migrations should be frozen in time. alternatively, keep a "snapshot" db interface.
export async function up(db: <PERSON><PERSON><PERSON><any>): Promise<void> {
  await db.schema
    .alterTable('employees')
    .addColumn('is_admin', 'boolean', (col) => col.notNull().defaultTo(false))
    .execute()
}

// `any` is required here since migrations should be frozen in time. alternatively, keep a "snapshot" db interface.
export async function down(db: Kysely<any>): Promise<void> {
  await db.schema.alterTable('employees').dropColumn('is_admin').execute()
}

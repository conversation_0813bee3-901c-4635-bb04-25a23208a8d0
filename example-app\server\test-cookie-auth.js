import axios from 'axios'

const CLIENT_SERVER_URL = 'http://localhost:3001'

// Test credentials
const TEST_CREDENTIALS = {
  username: 'william de jesus.rivera marroquin',
  password: 'admin123',
  client_id: '07b2f434-ec60-4cac-aa53-f17008ab5e6d',
  client_secret:
    '6eac4814cddc514c84cffaf691aae3b3d7dd83b12c56a3ea93f3aa226c0576fa',
}

async function testCookieAuthentication() {
  try {
    console.log('🧪 Testing HTTP-Only Cookie Authentication...\n')

    // Create axios instance to handle cookies
    const client = axios.create({
      baseURL: CLIENT_SERVER_URL,
      withCredentials: true, // Important for cookies
      timeout: 10000,
    })

    // Test 1: Login with credentials
    console.log('1. Testing login with credentials...')
    const loginResponse = await client.post('/api/auth/login', TEST_CREDENTIALS)

    console.log('✅ Login successful')
    console.log(`   Message: ${loginResponse.data.message}`)
    console.log(`   Authenticated: ${loginResponse.data.authenticated}`)
    console.log(`   Token Source: ${loginResponse.data.token_source}`)

    if (loginResponse.data.employee) {
      console.log(`   Employee: ${loginResponse.data.employee.full_name}`)
      console.log(
        `   Display Name: ${loginResponse.data.employee.display_name}`
      )
    }

    // Test 2: Check authentication status
    console.log('\n2. Testing authentication status...')
    const meResponse = await client.get('/api/auth/me')

    console.log('✅ Authentication status retrieved')
    console.log(`   Authenticated: ${meResponse.data.authenticated}`)

    if (meResponse.data.employee) {
      console.log(`   Employee: ${meResponse.data.employee.full_name}`)
      console.log(`   Token expires: ${meResponse.data.token_expires_at}`)
    }

    // Test 3: Access protected endpoint with cookie
    console.log('\n3. Testing protected endpoint with cookie...')
    const profileResponse = await client.get('/api/profile')

    console.log('✅ Protected endpoint accessed via cookie')
    console.log(`   Employee: ${profileResponse.data.employee.full_name}`)
    console.log(`   Data Source: ${profileResponse.data.employee.data_source}`)

    // Test 4: Test dashboard endpoint
    console.log('\n4. Testing dashboard endpoint...')
    const dashboardResponse = await client.get('/api/dashboard')

    console.log('✅ Dashboard accessed via cookie')
    console.log(
      `   Welcome: ${dashboardResponse.data.dashboard.welcome_message}`
    )
    console.log(
      `   Employee: ${dashboardResponse.data.dashboard.employee_info.full_name}`
    )

    // Test 5: Test server info with cookie
    console.log('\n5. Testing server info with cookie...')
    const infoResponse = await client.get('/api/info')

    console.log('✅ Server info retrieved with cookie authentication')
    console.log(`   Authenticated: ${infoResponse.data.authenticated}`)
    console.log(`   Employee: ${infoResponse.data.employee?.full_name}`)
    console.log(`   Data Source: ${infoResponse.data.employee?.source}`)

    // Test 6: Logout
    console.log('\n6. Testing logout...')
    const logoutResponse = await client.post('/api/auth/logout')

    console.log('✅ Logout successful')
    console.log(`   Message: ${logoutResponse.data.message}`)
    console.log(`   Authenticated: ${logoutResponse.data.authenticated}`)

    // Test 7: Verify logout worked
    console.log('\n7. Testing authentication status after logout...')
    const meAfterLogoutResponse = await client.get('/api/auth/me')

    console.log('✅ Authentication status after logout')
    console.log(`   Authenticated: ${meAfterLogoutResponse.data.authenticated}`)
    console.log(`   Employee: ${meAfterLogoutResponse.data.employee || 'None'}`)

    console.log('\n🎉 All HTTP-only cookie authentication tests passed!')
    console.log('\n📋 Test Summary:')
    console.log('   ✅ Login with credentials sets HTTP-only cookie')
    console.log('   ✅ Employee information available to frontend via /auth/me')
    console.log('   ✅ Protected endpoints work with cookie authentication')
    console.log('   ✅ JWT token stored securely in HTTP-only cookie')
    console.log('   ✅ Logout clears the cookie properly')
    console.log(
      '   ✅ Frontend can access employee name without token exposure'
    )
  } catch (error) {
    console.error('❌ Cookie authentication test failed:', error.message)
    if (error.response) {
      console.error('Response status:', error.response.status)
      console.error('Response data:', error.response.data)
    }
    process.exit(1)
  }
}

testCookieAuthentication()

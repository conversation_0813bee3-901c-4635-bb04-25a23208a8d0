import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { employeeService } from '../../services/employeeService'
import { createTestEmployee, cleanupTestEmployees } from '../setup'

// Mock console methods to capture output
const mockConsoleLog = vi.fn()
const mockConsoleError = vi.fn()
const mockProcessExit = vi.fn()

vi.stubGlobal('console', {
  log: mockConsoleLog,
  error: mockConsoleError
})

vi.stubGlobal('process', {
  ...process,
  argv: [],
  exit: mockProcessExit
})

describe('Update Password CLI Utility', () => {
  let testEmployeeId: number

  beforeEach(async () => {
    // Clear mocks
    mockConsoleLog.mockClear()
    mockConsoleError.mockClear()
    mockProcessExit.mockClear()

    // Create test employee
    testEmployeeId = await createTestEmployee({
      first_name: '<PERSON><PERSON><PERSON>',
      last_name: 'User',
      email: '<EMAIL>'
    })

    // Set username for the test employee
    await employeeService.setUsername(testEmployeeId, 'cliuser')
  })

  afterEach(async () => {
    await cleanupTestEmployees()
  })

  describe('Password Update Functionality', () => {
    it('should update password successfully with valid inputs', async () => {
      // Mock process.argv
      process.argv = ['node', 'updatePassword.ts', testEmployeeId.toString(), 'newPassword123']

      // Import and run the function
      const { updateEmployeePassword } = await import('../../utils/updatePassword')
      await updateEmployeePassword()

      // Check that success messages were logged
      expect(mockConsoleLog).toHaveBeenCalledWith('🔍 Looking up employee...')
      expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('✅ Found employee:'))
      expect(mockConsoleLog).toHaveBeenCalledWith('🔐 Updating password...')
      expect(mockConsoleLog).toHaveBeenCalledWith('✅ Password updated successfully!')
      expect(mockProcessExit).toHaveBeenCalledWith(0)
    })

    it('should show usage when no arguments provided', async () => {
      // Mock process.argv with no arguments
      process.argv = ['node', 'updatePassword.ts']

      const { updateEmployeePassword } = await import('../../utils/updatePassword')
      await updateEmployeePassword()

      // Check that usage message was shown
      expect(mockConsoleLog).toHaveBeenCalledWith('❌ Error: Missing required arguments')
      expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('Usage:'))
      expect(mockProcessExit).toHaveBeenCalledWith(0)
    })

    it('should reject invalid employee ID format', async () => {
      // Mock process.argv with invalid employee ID
      process.argv = ['node', 'updatePassword.ts', 'invalid', 'password123']

      const { updateEmployeePassword } = await import('../../utils/updatePassword')
      await updateEmployeePassword()

      // Check that error message was shown
      expect(mockConsoleLog).toHaveBeenCalledWith('❌ Error: employee_id must be a positive number')
      expect(mockProcessExit).toHaveBeenCalledWith(0)
    })

    it('should reject password that is too short', async () => {
      // Mock process.argv with short password
      process.argv = ['node', 'updatePassword.ts', testEmployeeId.toString(), '123']

      const { updateEmployeePassword } = await import('../../utils/updatePassword')
      await updateEmployeePassword()

      // Check that error message was shown
      expect(mockConsoleLog).toHaveBeenCalledWith('❌ Error: Password must be at least 6 characters long')
      expect(mockProcessExit).toHaveBeenCalledWith(0)
    })

    it('should handle non-existent employee', async () => {
      // Mock process.argv with non-existent employee ID
      process.argv = ['node', 'updatePassword.ts', '99999', 'password123']

      const { updateEmployeePassword } = await import('../../utils/updatePassword')
      await updateEmployeePassword()

      // Check that error message was shown
      expect(mockConsoleLog).toHaveBeenCalledWith('🔍 Looking up employee...')
      expect(mockConsoleLog).toHaveBeenCalledWith('❌ Error: Employee with ID 99999 not found or inactive')
      expect(mockProcessExit).toHaveBeenCalledWith(0)
    })
  })

  describe('Input Validation', () => {
    it('should validate employee ID is positive number', async () => {
      process.argv = ['node', 'updatePassword.ts', '0', 'password123']

      const { updateEmployeePassword } = await import('../../utils/updatePassword')
      await updateEmployeePassword()

      expect(mockConsoleLog).toHaveBeenCalledWith('❌ Error: employee_id must be a positive number')
    })

    it('should validate employee ID is not negative', async () => {
      process.argv = ['node', 'updatePassword.ts', '-1', 'password123']

      const { updateEmployeePassword } = await import('../../utils/updatePassword')
      await updateEmployeePassword()

      expect(mockConsoleLog).toHaveBeenCalledWith('❌ Error: employee_id must be a positive number')
    })

    it('should show helpful examples in usage', async () => {
      process.argv = ['node', 'updatePassword.ts']

      const { updateEmployeePassword } = await import('../../utils/updatePassword')
      await updateEmployeePassword()

      expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('Examples:'))
      expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('npm run employee:update-password 123'))
    })
  })

  describe('Error Handling', () => {
    it('should handle database errors gracefully', async () => {
      // Mock employeeService to throw an error
      const originalGetEmployeeById = employeeService.getEmployeeById
      employeeService.getEmployeeById = vi.fn().mockRejectedValue(new Error('Database connection failed'))

      process.argv = ['node', 'updatePassword.ts', testEmployeeId.toString(), 'password123']

      const { updateEmployeePassword } = await import('../../utils/updatePassword')
      await updateEmployeePassword()

      expect(mockConsoleError).toHaveBeenCalledWith(expect.stringContaining('💥 Failed to update employee password:'))
      expect(mockProcessExit).toHaveBeenCalledWith(0)

      // Restore original method
      employeeService.getEmployeeById = originalGetEmployeeById
    })
  })

  describe('Success Scenarios', () => {
    it('should display employee information when found', async () => {
      process.argv = ['node', 'updatePassword.ts', testEmployeeId.toString(), 'newPassword123']

      const { updateEmployeePassword } = await import('../../utils/updatePassword')
      await updateEmployeePassword()

      expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('✅ Found employee: CLI USER'))
      expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('Username: cliuser'))
    })

    it('should show helpful completion message', async () => {
      process.argv = ['node', 'updatePassword.ts', testEmployeeId.toString(), 'newPassword123']

      const { updateEmployeePassword } = await import('../../utils/updatePassword')
      await updateEmployeePassword()

      expect(mockConsoleLog).toHaveBeenCalledWith('💡 The employee can now log in with their new password')
    })
  })
})

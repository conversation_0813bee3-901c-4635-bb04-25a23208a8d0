import axios from 'axios'
import * as crypto from 'crypto'
import { OAuthClient } from '../db/models/oauth_clients'

export interface WebhookEvent {
  event: string
  data: {
    client_id: string
    employee_id?: number
    token_id?: string
    access_token?: string
    refresh_token?: string
    revoked_at: string
    reason?: string
  }
  timestamp: string
  webhook_id: string
}

export class WebhookService {
  private readonly maxRetries = 3
  private readonly timeoutMs = 10000 // 10 seconds

  /**
   * Send webhook notification to client
   */
  async sendWebhook(
    client: OAuthClient,
    event: WebhookEvent
  ): Promise<boolean> {
    if (!client.webhook_url) {
      return false
    }

    // Check if client is subscribed to this event
    if (client.webhook_events && client.webhook_events.length > 0) {
      if (!client.webhook_events.includes(event.event)) {
        console.log(
          `Client ${client.client_id} not subscribed to event: ${event.event}`
        )
        return false
      }
    }

    const payload = JSON.stringify(event)
    const signature = this.generateSignature(payload, client.webhook_secret)

    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'User-Agent': 'OAuth2-Server-Webhook/1.0',
      'X-Webhook-Event': event.event,
      'X-Webhook-Timestamp': event.timestamp,
      'X-Webhook-ID': event.webhook_id,
    }

    if (signature) {
      headers['X-Webhook-Signature'] = signature
    }

    let lastError: Error | null = null

    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        console.log(
          `Sending webhook to ${client.webhook_url} (attempt ${attempt}/${this.maxRetries})`
        )

        const response = await axios.post(client.webhook_url, payload, {
          headers,
          timeout: this.timeoutMs,
          validateStatus: (status) => status >= 200 && status < 300,
        })

        console.log(
          `Webhook sent successfully to ${client.webhook_url}, status: ${response.status}`
        )
        return true
      } catch (error) {
        lastError = error as Error
        console.error(
          `Webhook attempt ${attempt} failed for ${client.webhook_url}:`,
          (error as Error).message
        )

        if (attempt < this.maxRetries) {
          // Exponential backoff: 1s, 2s, 4s
          const delay = Math.pow(2, attempt - 1) * 1000
          await this.sleep(delay)
        }
      }
    }

    console.error(
      `All webhook attempts failed for ${client.webhook_url}:`,
      lastError?.message
    )
    return false
  }

  /**
   * Send token revocation webhook
   */
  async sendTokenRevocationWebhook(
    client: OAuthClient,
    tokenData: {
      employee_id?: number
      token_id?: string
      access_token?: string
      refresh_token?: string
      reason?: string
    }
  ): Promise<boolean> {
    const event: WebhookEvent = {
      event: 'token.revoked',
      data: {
        client_id: client.client_id,
        employee_id: tokenData.employee_id,
        token_id: tokenData.token_id,
        access_token: tokenData.access_token
          ? this.maskToken(tokenData.access_token)
          : undefined,
        refresh_token: tokenData.refresh_token
          ? this.maskToken(tokenData.refresh_token)
          : undefined,
        revoked_at: new Date().toISOString(),
        reason: tokenData.reason || 'manual_revocation',
      },
      timestamp: new Date().toISOString(),
      webhook_id: crypto.randomUUID(),
    }

    return await this.sendWebhook(client, event)
  }

  /**
   * Send refresh token revocation webhook
   */
  async sendRefreshTokenRevocationWebhook(
    client: OAuthClient,
    tokenData: {
      employee_id?: number
      token_id?: string
      refresh_token?: string
      reason?: string
    }
  ): Promise<boolean> {
    const event: WebhookEvent = {
      event: 'refresh_token.revoked',
      data: {
        client_id: client.client_id,
        employee_id: tokenData.employee_id,
        token_id: tokenData.token_id,
        refresh_token: tokenData.refresh_token
          ? this.maskToken(tokenData.refresh_token)
          : undefined,
        revoked_at: new Date().toISOString(),
        reason: tokenData.reason || 'manual_revocation',
      },
      timestamp: new Date().toISOString(),
      webhook_id: crypto.randomUUID(),
    }

    return await this.sendWebhook(client, event)
  }

  /**
   * Generate HMAC signature for webhook payload
   */
  private generateSignature(
    payload: string,
    secret: string | null
  ): string | null {
    if (!secret) {
      return null
    }

    const hmac = crypto.createHmac('sha256', secret)
    hmac.update(payload)
    return `sha256=${hmac.digest('hex')}`
  }

  /**
   * Mask sensitive token data for webhook payload
   */
  private maskToken(token: string): string {
    if (token.length <= 8) {
      return '***'
    }
    return `${token.substring(0, 4)}...${token.substring(token.length - 4)}`
  }

  /**
   * Sleep for specified milliseconds
   */
  private sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms))
  }

  /**
   * Verify webhook signature
   */
  static verifySignature(
    payload: string,
    signature: string,
    secret: string
  ): boolean {
    if (!signature.startsWith('sha256=')) {
      return false
    }

    const expectedSignature = signature.substring(7)
    const hmac = crypto.createHmac('sha256', secret)
    hmac.update(payload)
    const computedSignature = hmac.digest('hex')

    return crypto.timingSafeEqual(
      Buffer.from(expectedSignature, 'hex'),
      Buffer.from(computedSignature, 'hex')
    )
  }
}

export const webhookService = new WebhookService()

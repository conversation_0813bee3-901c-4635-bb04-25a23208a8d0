{"name": "oauth2-app-example", "version": "1.0.0", "description": "Full-stack OAuth 2.0 application example with React client and Express server", "type": "module", "scripts": {"dev": "concurrently \"npm run dev:server\" \"npm run dev:client\"", "dev:server": "cd server && npm run dev", "dev:client": "cd client && npm run dev", "start": "concurrently \"npm run start:server\" \"npm run start:client\"", "start:server": "cd server && npm start", "start:client": "cd client && npm run preview", "build": "npm run build:client", "build:client": "cd client && npm run build", "test": "npm run test:server", "test:server": "cd server && npm test", "test:client": "cd client && npm test", "install:all": "npm install && npm run install:server && npm run install:client", "install:server": "cd server && npm install", "install:client": "cd client && npm install", "clean": "npm run clean:server && npm run clean:client", "clean:server": "cd server && rm -rf node_modules package-lock.json", "clean:client": "cd client && rm -rf node_modules package-lock.json dist"}, "devDependencies": {"concurrently": "^8.2.2"}, "keywords": ["oauth2", "jwt", "rs256", "react", "express", "full-stack", "authentication", "authorization"], "author": "OAuth Example", "license": "MIT", "workspaces": ["client", "server"]}
import { Generated, Insertable, Selectable, Updateable } from 'kysely'

export interface PasswordResetCodesTable {
  id: Generated<number>
  employee_id: number
  reset_code: string
  expires_at: string
  used: boolean
  created_at: Generated<string>
  used_at: string | null
}

export type PasswordResetCode = Selectable<PasswordResetCodesTable>
export type NewPasswordResetCode = Insertable<PasswordResetCodesTable>
export type PasswordResetCodeUpdate = Updateable<PasswordResetCodesTable>

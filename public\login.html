<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>VarPro OAuth Login</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
          Oxygen, Ubuntu, Cantarell, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px;
      }

      .login-container {
        background: white;
        border-radius: 12px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        padding: 40px;
        width: 100%;
        max-width: 400px;
      }

      .logo {
        text-align: center;
        margin-bottom: 30px;
      }

      .logo h1 {
        color: #333;
        font-size: 28px;
        font-weight: 700;
        margin-bottom: 8px;
      }

      .logo p {
        color: #666;
        font-size: 14px;
      }

      .form-group {
        margin-bottom: 20px;
      }

      label {
        display: block;
        margin-bottom: 6px;
        color: #333;
        font-weight: 500;
        font-size: 14px;
      }

      input[type='text'],
      input[type='password'] {
        width: 100%;
        padding: 12px 16px;
        border: 2px solid #e1e5e9;
        border-radius: 8px;
        font-size: 16px;
        transition: border-color 0.3s ease;
        background: #f8f9fa;
      }

      input[type='text']:focus,
      input[type='password']:focus {
        outline: none;
        border-color: #667eea;
        background: white;
      }

      .login-btn {
        width: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 14px;
        border-radius: 8px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: transform 0.2s ease, box-shadow 0.2s ease;
        margin-top: 10px;
      }

      .login-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
      }

      .login-btn:active {
        transform: translateY(0);
      }

      .login-btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
      }

      .error-message {
        background: #fee;
        color: #c53030;
        padding: 12px;
        border-radius: 8px;
        margin-bottom: 20px;
        font-size: 14px;
        border: 1px solid #fed7d7;
      }

      .loading {
        display: none;
        text-align: center;
        margin-top: 10px;
      }

      .loading.show {
        display: block;
      }

      .spinner {
        display: inline-block;
        width: 20px;
        height: 20px;
        border: 2px solid #f3f3f3;
        border-top: 2px solid #667eea;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-right: 8px;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      .footer {
        text-align: center;
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #e1e5e9;
      }

      .footer p {
        color: #666;
        font-size: 12px;
      }

      .admin-link {
        display: block;
        text-align: center;
        margin-top: 20px;
        color: #667eea;
        text-decoration: none;
        font-size: 14px;
      }

      .admin-link:hover {
        text-decoration: underline;
      }
    </style>
  </head>
  <body>
    <div class="login-container">
      <div class="logo">
        <h1>VarPro</h1>
        <p>OAuth Authentication</p>
      </div>

      <div id="error-container"></div>

      <form id="loginForm">
        <div class="form-group">
          <label for="username">Username</label>
          <input type="text" id="username" name="username" required />
        </div>

        <div class="form-group">
          <label for="password">Password</label>
          <input type="password" id="password" name="password" required />
        </div>

        <button type="submit" class="login-btn" id="loginBtn">Sign In</button>

        <div class="loading" id="loading">
          <div class="spinner"></div>
          <span>Signing in...</span>
        </div>
      </form>

      <a href="/admin" class="admin-link">Admin Panel →</a>

      <div class="footer">
        <p>&copy; 2024 VarPro. All rights reserved.</p>
      </div>
    </div>

    <script>
      // OAuth configuration
      const OAUTH_CONFIG = {
        serverUrl: 'http://localhost:3230',
        clientId: 'varpro-client',
        redirectUri: window.location.origin + '/callback.html',
      }

      // DOM elements
      const loginForm = document.getElementById('loginForm')
      const loginBtn = document.getElementById('loginBtn')
      const loading = document.getElementById('loading')
      const errorContainer = document.getElementById('error-container')

      // Show error message
      function showError(message) {
        errorContainer.innerHTML = `<div class="error-message">${message}</div>`
      }

      // Clear error message
      function clearError() {
        errorContainer.innerHTML = ''
      }

      // Set loading state
      function setLoading(isLoading) {
        if (isLoading) {
          loginBtn.disabled = true
          loginBtn.textContent = ''
          loading.classList.add('show')
        } else {
          loginBtn.disabled = false
          loginBtn.textContent = 'Sign In'
          loading.classList.remove('show')
        }
      }

      // Handle form submission - redirect to OAuth authorization server
      loginForm.addEventListener('submit', async (e) => {
        e.preventDefault()

        // Generate state parameter for security
        const state = generateRandomString(32)
        localStorage.setItem('oauth_state', state)

        // Redirect to OAuth authorization server
        const authUrl = new URL(`${OAUTH_CONFIG.serverUrl}/oauth/authorize`)
        authUrl.searchParams.set('response_type', 'code')
        authUrl.searchParams.set('client_id', OAUTH_CONFIG.clientId)
        authUrl.searchParams.set('redirect_uri', OAUTH_CONFIG.redirectUri)
        authUrl.searchParams.set('scope', 'read write')
        authUrl.searchParams.set('state', state)

        window.location.href = authUrl.toString()
      })

      // Generate random string for state parameter
      function generateRandomString(length) {
        const chars =
          'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
        let result = ''
        for (let i = 0; i < length; i++) {
          result += chars.charAt(Math.floor(Math.random() * chars.length))
        }
        return result
      }

      // Check if user is already logged in
      window.addEventListener('load', () => {
        const token = localStorage.getItem('access_token')
        if (token) {
          // Redirect to dashboard if already logged in
          window.location.href = '/dashboard.html'
        }
      })
    </script>
  </body>
</html>
